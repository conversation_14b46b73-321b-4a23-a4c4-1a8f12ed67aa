<formly-form #formly class="flex-1 self-stretch"
  [form]="form"
  [fields]="schema"
  [model]="customSmtp"
  (modelChange)="onFormChange(customSmtp)">
</formly-form>

<div class="flex justify-start">
  <div ngmButtonGroup>
    <button mat-stroked-button displayDensity="cosy" color="accent" [disabled]="isValidated()"
      (click)="validateSmtp()">
      {{ 'PAC.ACTIONS.Validate' | translate: {Default: 'Validate'} }}
    </button>
  
    <button mat-raised-button displayDensity="cosy" color="primary" [disabled]="!isValidated() || form.invalid || form.pristine"
      (click)="onSubmit()">
      {{ 'PAC.ACTIONS.Save' | translate: {Default: 'Save'} }}
    </button>
  </div>
</div>
