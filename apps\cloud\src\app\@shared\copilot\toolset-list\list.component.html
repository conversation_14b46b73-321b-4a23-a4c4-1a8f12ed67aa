@if (toolsetList()?.length) {
  <ul cdkListbox cdkListboxMultiple class="ngm-cdk-listbox"
    [(ngModel)]="toolsets"
    [cdkListboxCompareWith]="compareId"
  >
    @for (item of toolsetList(); track item.id) {
      <li [cdkOption]="item" #option="cdkOption" class="ngm-cdk-option flex items-center cursor-pointer px-8 py-3 rounded-md
       hover:bg-black/5 dark:hover:bg-white/10"
        [class.cdk-option-selected]="option.isSelected()"
        [cdkOptionDisabled]="disabled()"
      >
        <emoji-avatar [avatar]="item.avatar" small class="shrink-0 mr-2 rounded-lg overflow-hidden" />
        <span class="whitespace-nowrap text-ellipsis overflow-hidden" [title]="item.name">{{item.name}}</span>

        @if (option.isSelected()) {
          <mat-icon class="absolute right-1" fontSet="material-icons-outlined" color="accent">check_small</mat-icon>
        }
      </li>
    }
  </ul>
}