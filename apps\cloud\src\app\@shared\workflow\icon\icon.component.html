@switch (type()) {
  @case (eWorkflowNodeTypeEnum.ASSIGNER) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-blue-500
      w-full h-full rounded-lg shadow-sm shrink-0">
      <i class="ri-equal-line"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.IF_ELSE) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-teal-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg" class="w-3.5 h-3.5">
        <g id="icons/if-else"><path id="Vector (Stroke)" fill-rule="evenodd" clip-rule="evenodd" d="M8.16667 2.98975C7.80423 2.98975 7.51042 2.69593 7.51042 2.3335C7.51042 1.97106 7.80423 1.67725 8.16667 1.67725H11.0833C11.4458 1.67725 11.7396 1.97106 11.7396 2.3335V5.25016C11.7396 5.6126 11.4458 5.90641 11.0833 5.90641C10.7209 5.90641 10.4271 5.6126 10.4271 5.25016V3.91782L7.34474 7.00016L10.4271 10.0825V8.75016C10.4271 8.38773 10.7209 8.09391 11.0833 8.09391C11.4458 8.09391 11.7396 8.38773 11.7396 8.75016V11.6668C11.7396 12.0293 11.4458 12.3231 11.0833 12.3231H8.16667C7.80423 12.3231 7.51042 12.0293 7.51042 11.6668C7.51042 11.3044 7.80423 11.0106 8.16667 11.0106H9.49901L6.14484 7.65641H1.75C1.38756 7.65641 1.09375 7.3626 1.09375 7.00016C1.09375 6.63773 1.38756 6.34391 1.75 6.34391H6.14484L9.49901 2.98975H8.16667Z" fill="currentColor"></path></g>
      </svg>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.SPLITTER) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-indigo-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-scissors-cut-fill"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.ITERATING) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-indigo-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-repeat-2-line"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.ANSWER) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-lime-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-chat-ai-fill"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.NOTE) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-orange-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-sticky-note-add-line"></i>
    </div>
  }
   @case (eWorkflowNodeTypeEnum.CLASSIFIER) {
    <div class="shrink-0 flex items-center justify-center border-[0.5px] border-white/2 text-white bg-green-400
      w-full h-full rounded-lg shadow-sm">
      <i class="ri-list-radio"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.KNOWLEDGE) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-green-400
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-book-shelf-line"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.CODE) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-blue-400
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-code-s-slash-line"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.HTTP) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-teal-400
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-radar-fill"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.SUBFLOW) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-teal-500
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-exchange-2-fill"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.TEMPLATE) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-blue-400
      w-full h-full rounded-lg shadow-sm
      shrink-0">
      <i class="ri-font-sans-serif"></i>
    </div>
  }
  @case (eWorkflowNodeTypeEnum.TOOL) {
    <div class="flex items-center justify-center border-[0.5px] border-white/2 text-white bg-teal-500
      w-full h-full rounded-lg shadow-sm shrink-0">
      <i class="ri-hammer-fill"></i>
    </div>
  }
  @default {
    <div>Unimplement type ({{type()}})</div>
  }
}