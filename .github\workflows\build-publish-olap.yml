name: Publish olap docker image

on:
  push:
    branches: [ "olap" ]

jobs:

  ocap-olap:

    runs-on: ubuntu-latest
    if: github.repository == 'xpert-ai/xpert' && github.event_name == 'push'
    steps:
    - uses: actions/checkout@v4
    - name: Build
      uses: docker/build-push-action@v3
      with:
          context: .
          file: ./.deploy/olap/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
              ghcr.io/meta-d/ocap-olap:latest
              metadc/ocap-olap:latest
              registry.cn-hangzhou.aliyuncs.com/metad/ocap-olap:latest
          cache-from: type=registry,ref=metadc/ocap-olap:latest
          cache-to: type=inline

    - name: Docker images list
      run: |
          sudo docker image list

    - name: Login to DockerHub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}

    - name: Push to Docker Hub Registry
      env:
        GITHUB_REF_NAME: ${{ github.ref_name }}
      run: |
        docker tag metadc/ocap-olap:latest metadc/ocap-olap:$GITHUB_REF_NAME
        docker push metadc/ocap-olap:$GITHUB_REF_NAME

    - name: Login to GitHub Container Registry
      uses: docker/login-action@v2
      with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

    - name: Push to Github Registry
      run: |
          docker tag ghcr.io/meta-d/ocap-olap:latest ghcr.io/meta-d/ocap-olap:$GITHUB_REF_NAME
          docker push ghcr.io/meta-d/ocap-olap:$GITHUB_REF_NAME

    - uses: aliyun/acr-login@v1
      with:
        login-server: https://registry.cn-hangzhou.aliyuncs.com
        username: "${{ secrets.ACR_USERNAME }}"
        password: "${{ secrets.ACR_PASSWORD }}"

    - name: Push to Aliyun Registry
      run: |
          docker tag registry.cn-hangzhou.aliyuncs.com/metad/ocap-olap:latest registry.cn-hangzhou.aliyuncs.com/metad/ocap-olap:$GITHUB_REF_NAME
          docker push registry.cn-hangzhou.aliyuncs.com/metad/ocap-olap:$GITHUB_REF_NAME
          