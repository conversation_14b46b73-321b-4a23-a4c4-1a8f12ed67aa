user  nginx;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;

  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

  access_log  /var/log/nginx/access.log  main;

  #gzip  on;

  server {
    listen              80;

    location ~* \.(gif|jpg|jpeg|png|css|js|ico)$ {
      root /srv/pangolin/;
    }

    location / {
      root /srv/pangolin;
      try_files $uri $uri/ /index.html;
    }
  }

}
