apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: adminer
  namespace: prod
  annotations:
    # URL重定向。
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  rules:
  - host: adminer.mtda.cloud
    http:
      paths:
    # 在Ingress Controller的版本≥0.22.0之后，path中需要使用正则表达式定义路径，并在rewrite-target中结合捕获组一起使用。
      - path: /
        backend:
          service: 
            name: db-adminer
            port: 
              number: 8084
        pathType: ImplementationSpecific
  tls:
  - hosts:
      - adminer.mtda.cloud
    secretName: adminer-tls
---
apiVersion: v1
kind: Service
metadata:
  name: metad-prod-api-lb
spec:
  type: ClusterIP
  selector:
    app: metad-prod-api
  ports:
    - name: http
      protocol: TCP
      port: 3000
      targetPort: 3000
---
apiVersion: v1
kind: Service
metadata:
  name: olap
spec:
  type: ClusterIP
  selector:
    app: metad-prod-olap
  ports:
    - name: http
      protocol: TCP
      port: 8080
      targetPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: webapp
spec:
  type: ClusterIP
  selector:
    app: metad-prod-webapp
  ports:
    - name: webapp
      protocol: TCP
      port: 4200
      targetPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: metad-website-lb
spec:
  type: ClusterIP
  selector:
    app: metad-prod-website
  ports:
    - name: http
      protocol: TCP
      port: 80
      targetPort: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metad-prod-api
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metad-prod-api
  template:
    metadata:
      labels:
        app: metad-prod-api
    spec:
      containers:
        - name: metad-prod-api
          image: registry.cn-hangzhou.aliyuncs.com/metad/ocap-api:2.4.4
          # imagePullPolicy: Always
          envFrom:
            - secretRef:
                name: ocap-secrets
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: REDIS_PASSWORD
                  optional: false
            - name: JWT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: JWT_SECRET
                  optional: false
            - name: JWT_REFRESH_SECRET
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: JWT_REFRESH_SECRET
                  optional: false
            - name: GITHUB_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: GITHUB_CLIENT_ID
                  optional: false
            - name: GITHUB_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: GITHUB_CLIENT_SECRET
                  optional: false
            - name: DINGTALK_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: DINGTALK_CLIENT_ID
                  optional: false
            - name: DINGTALK_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: DINGTALK_CLIENT_SECRET
                  optional: false
            - name: FEISHU_CLIENT_ID
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: FEISHU_CLIENT_ID
                  optional: false
            - name: FEISHU_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: FEISHU_CLIENT_SECRET
                  optional: false  
            - name: API_BASE_URL
              value: 'https://api.mtda.cloud'
            - name: CLIENT_BASE_URL
              value: 'https://app.mtda.cloud'
            - name: API_HOST
              value: 0.0.0.0
            - name: DEMO
              value: 'false'
            - name: NODE_ENV
              value: 'production'
            - name: LOGGER_LEVEL
              value: 'debug'
            - name: ADMIN_PASSWORD_RESET
              value: 'true'
            - name: LOG_LEVEL
              value: 'info'
            - name: DB_URI
              value: '$DB_URI'
            - name: DB_HOST
              value: 'postgres'
            - name: DB_SSL_MODE
              value: '$DB_SSL_MODE'
            - name: DB_CA_CERT
              value: '$DB_CA_CERT'
            - name: DB_TYPE
              value: 'postgres'
            - name: DB_PORT
              value: '5432'
            - name: REDIS_HOST
              value: 'redis'
            - name: REDIS_PORT
              value: '6379'
            - name: OLAP_HOST
              value: 'olap'
            - name: OLAP_PORT
              value: '8080'
            - name: GITHUB_CALLBACK_URL
              value: 'https://api.mtda.cloud/api/auth/github/callback'
            - name: DINGTALK_REDIRECT_URL
              value: 'https://api.mtda.cloud/api/auth/dingtalk/callback'
            - name: FEISHU_REDIRECT_URL
              value: 'https://api.mtda.cloud/api/auth/feishu/callback'
            - name: FEISHU_APP_TYPE
              value: 'internal'
            - name: jwtExpiresIn
              value: '1h'
            - name: jwtRefreshExpiresIn
              value: '7d'
          ports:
            - containerPort: 3000
              protocol: TCP
          # livenessProbe:
          #   httpGet:
          #     path: /
          #     scheme: HTTP
          #     port: 3000
          #   initialDelaySeconds: 3
          #   periodSeconds: 60
          #   successThreshold: 1
          #   failureThreshold: 5
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metad-prod-webapp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metad-prod-webapp
  template:
    metadata:
      labels:
        app: metad-prod-webapp
    spec:
      containers:
        - name: metad-prod-webapp
          image: registry.cn-hangzhou.aliyuncs.com/metad/ocap-webapp:2.4.4
          # imagePullPolicy: Always
          ports:
            - containerPort: 80
              protocol: TCP
          env:
            - name: API_BASE_URL
              value: 'https://api.mtda.cloud'
            - name: ENABLE_LOCAL_AGENT
              value: 'true'
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metad-prod-website
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metad-prod-website
  template:
    metadata:
      labels:
        app: metad-prod-website
    spec:
      containers:
        - name: metad-prod-website
          image: registry.cn-hangzhou.aliyuncs.com/metad/website:2.4.0
          ports:
            - containerPort: 80
              protocol: TCP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: metad-prod-olap
spec:
  replicas: 1
  selector:
    matchLabels:
      app: metad-prod-olap
  template:
    metadata:
      labels:
        app: metad-prod-olap
    spec:
      containers:
        - name: metad-prod-olap
          image: registry.cn-hangzhou.aliyuncs.com/metad/ocap-olap:2.4.4
          ports:
            - containerPort: 8080
              protocol: TCP
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: REDIS_PASSWORD
                  optional: false
            - name: REDIS_HOST
              value: 'redis'
          livenessProbe:
            httpGet:
              path: /
              scheme: HTTP
              port: 8080
            initialDelaySeconds: 3
            periodSeconds: 60
            successThreshold: 1
            failureThreshold: 5
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
spec:
  type: ClusterIP
  selector:
    app: postgres
  ports:
    - protocol: TCP
      port: 5432
---
apiVersion: v1
kind: Service
metadata:
  name: db-adminer
spec:
  type: ClusterIP
  selector:
    app: db-adminer
  ports:
    - protocol: TCP
      port: 8084
      targetPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  type: ClusterIP
  selector:
    app: redis
  ports:
    - protocol: TCP
      name: redis
      port: 6379
      targetPort: 6379
    - protocol: TCP
      name: insight
      port: 8001
      targetPort: 8001
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: registry.cn-hangzhou.aliyuncs.com/metad/pgvector:pg12
          # imagePullPolicy: Always
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_USER
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: DB_USER
                  optional: false
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: DB_PASS
                  optional: false
            - name: POSTGRES_DB
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: DB_NAME
                  optional: false
            - name: PGDATA
              value: /var/lib/postgresql/data/pgdata
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: postgredb
      volumes:
        - name: postgredb
          persistentVolumeClaim:
            claimName: db-pvc
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: db-adminer
spec:
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  replicas: 1
  selector:
    matchLabels:
      app: db-adminer
  template:
    metadata:
      labels:
        app: db-adminer
    spec:
      containers:
        - name: adminer
          image: adminer:latest
          ports:
            - containerPort: 8080
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
        - name: redis
          image: registry.cn-hangzhou.aliyuncs.com/metad/redis-stack:6.2.6-v15-x86_64
          # imagePullPolicy: Always
          ports:
            - containerPort: 6379
            - containerPort: 8001
          env:
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: ocap-secrets
                  key: REDIS_PASSWORD
          command:
            - redis-server
          args:
            - --requirepass
            - $(REDIS_PASSWORD)
