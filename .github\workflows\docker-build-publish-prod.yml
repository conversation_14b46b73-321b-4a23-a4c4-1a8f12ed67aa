name: Build and Publish Docker Images Prod

on:
  push:
    branches: [ "main" ]
    tags:
      - '*'

permissions:
  contents: read
  packages: write

jobs:
  build:
    runs-on: ubuntu-latest
    if: github.repository == 'xpert-ai/xpert' && github.event_name == 'push' && !contains(github.ref, 'beta')
    environment: production
    strategy:
      fail-fast: false
      matrix:
        service:
          - { name: "xpert-api", context: ".deploy/api", image_name: "xpert-api" }
          - { name: "xpert-webapp", context: ".deploy/webapp", image_name: "xpert-webapp" }
          - { name: "ocap-olap", context: ".deploy/olap", image_name: "ocap-olap" }
    steps:
      - uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
    
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build and Push
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ${{ matrix.service.context }}/Dockerfile
          load: true
          platforms: linux/amd64
          tags: |
            metadc/${{ matrix.service.image_name }}:latest
            ghcr.io/xpert-ai/${{ matrix.service.image_name }}:latest
            registry.cn-hangzhou.aliyuncs.com/metad/${{ matrix.service.image_name }}:latest
          cache-from: type=registry,ref=metadc/${{ matrix.service.image_name }}:latest
          cache-to: type=inline

      - name: Docker images list
        run: |
          sudo docker image list

      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ vars.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Push to Docker Hub Registry
        env:
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          docker push metadc/${{ matrix.service.image_name }}:latest
          docker tag metadc/${{ matrix.service.image_name }}:latest metadc/${{ matrix.service.image_name }}:$GITHUB_REF_NAME
          docker push metadc/${{ matrix.service.image_name }}:$GITHUB_REF_NAME

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
            registry: ghcr.io
            username: ${{ github.actor }}
            password: ${{ secrets.GITHUB_TOKEN }}

      - name: Push to GitHub Container Registry
        env:
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          docker push ghcr.io/xpert-ai/${{ matrix.service.image_name }}:latest
          docker tag ghcr.io/xpert-ai/${{ matrix.service.image_name }}:latest ghcr.io/xpert-ai/${{ matrix.service.image_name }}:$GITHUB_REF_NAME
          docker push ghcr.io/xpert-ai/${{ matrix.service.image_name }}:$GITHUB_REF_NAME

      - name: Login to Aliyun arc
        uses: aliyun/acr-login@v1
        with:
          login-server: https://registry.cn-hangzhou.aliyuncs.com
          username: "${{ secrets.ACR_USERNAME }}"
          password: "${{ secrets.ACR_PASSWORD }}"

      - name: Push to Aliyun arc
        env:
          GITHUB_REF_NAME: ${{ github.ref_name }}
        run: |
          docker push registry.cn-hangzhou.aliyuncs.com/metad/${{ matrix.service.image_name }}:latest
          docker tag registry.cn-hangzhou.aliyuncs.com/metad/${{ matrix.service.image_name }}:latest registry.cn-hangzhou.aliyuncs.com/metad/${{ matrix.service.image_name }}:$GITHUB_REF_NAME
          docker push registry.cn-hangzhou.aliyuncs.com/metad/${{ matrix.service.image_name }}:$GITHUB_REF_NAME
