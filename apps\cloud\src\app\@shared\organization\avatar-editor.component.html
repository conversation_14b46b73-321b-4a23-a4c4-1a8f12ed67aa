<div class="relative w-16 h-16 rounded-full overflow-hidden group border border-solid border-transparent hover:border-gray-200">
    <img class="" [src]="org()?.imageUrl || '/assets/images/illustrations/default-company-logo.svg'" alt="{{org?.name}}"/>
    <div class="absolute w-full h-full top-0 left-0 flex justify-center items-center cursor-pointer z-10
        opacity-0 bg-white/10 backdrop-blur-sm group-hover:opacity-100"
        [cdkMenuTriggerFor]="editorMenu"
    >
        <mat-icon fontSet="material-icons-outlined">edit</mat-icon>
    </div>
</div>

<input #fileUpload type="file" class="file-input invisible w-0"
    (change)="uploadAvatar($event)"
    (click)="fileUpload.value=null;">

<ng-template #editorMenu>
  <div cdkMenu displayDensity="cosy" class="ngm-cdk-menu p-2">
    <button cdkMenuItem class="ngm-cdk-menu-item px-2 py-1" (click)="fileUpload.click()">
      {{ 'PAC.ACTIONS.Upload' | translate: { Default: 'Upload' } }}
    </button>
    <button cdkMenuItem *ngIf="org()?.imageUrl" class="ngm-cdk-menu-item px-2 py-1" ngmAppearance="danger" (click)="remove()">
      {{ 'PAC.ACTIONS.Remove' | translate: { Default: 'Remove' } }}
    </button>
  </div>
</ng-template>
