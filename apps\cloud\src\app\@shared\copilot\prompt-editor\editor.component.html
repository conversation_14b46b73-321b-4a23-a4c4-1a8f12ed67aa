<div class="container grow flex flex-col rounded-xl bg-gray-50 overflow-hidden">
  <div class="shrink-0 flex justify-between items-center h-9 pl-3 pr-2 bg-gray-50 rounded-t-xl">
    <div class="flex items-center space-x-1">
      @if (role() === 'system') {
        <div class="h2 text-sm">{{'PAC.Copilot.Prompt' | translate: {Default: 'Prompt'} }}</div>
      } @else if (role() === 'ai') {
        <div class="flex items-center h-6 pl-1 pr-0.5 rounded-md font-semibold text-gray-700 cursor-pointer hover:bg-black/5">
          <div class="text-sm font-semibold text-gray-700 uppercase">ai</div>
        </div>
      } @else if (role() === 'human') {
        <div class="flex items-center h-6 pl-1 pr-0.5 rounded-md font-semibold text-gray-700 cursor-pointer hover:bg-black/5">
          <div class="text-sm font-semibold text-gray-700 uppercase">user</div>
        </div>
      } @else if(role()) {
        <div class="text-sm font-semibold text-gray-700 uppercase">{{role()}}</div>
      }
     
      <div class="p-[1px] w-4 h-4 flex items-center shrink-0 text-text-quaternary hover:text-text-tertiary"
        [matTooltip]="tooltip()"
        matTooltipPosition="above"
      >
        <i class="ri-question-line"></i>
      </div>
    </div>

    <div class="flex items-center">
      <div class="h-[18px] leading-[18px] px-1 rounded-md bg-gray-100 text-xs text-gray-500">{{promptLength()}}</div>

      @if (enableAi()) {
        <div class="flex space-x-1 items-center px-2 py-0.5 rounded-lg text-sm cursor-pointer group hover:bg-state-base-hover"
          (click)="generate()">
          <svg
            width="14"
            height="14"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            class="w-3.5 h-3.5 text-lg text-indigo-600"
          >
            <path
              opacity="0.5"
              d="M10.5402 2.95679L10.5402 2.95685C10.4455 3.05146 10.3424 3.13459 10.2314 3.2072C10.3429 3.27923 10.4468 3.36165 10.5422 3.45535L10.5402 2.95679ZM10.5402 2.95679C10.6348 2.86217 10.718 2.75907 10.7906 2.64807C10.8626 2.75955 10.945 2.86339 11.0387 2.95881L11.0388 2.95888C11.1304 3.05224 11.2302 3.13482 11.3377 3.20717C11.2297 3.27895 11.1292 3.36081 11.0367 3.45327L11.0366 3.45333C10.9442 3.5458 10.8623 3.64635 10.7905 3.75431M10.5402 2.95679L10.7905 3.75431M10.7905 3.75431C10.7182 3.64686 10.6356 3.54707 10.5422 3.45538L10.7905 3.75431Z"
              stroke="currentColor"
              stroke-width="1.25"
            ></path>
            <path
              d="M6.99659 2.85105C6.96323 2.55641 6.71414 2.33368 6.41758 2.33337C6.12107 2.33307 5.87146 2.55529 5.83751 2.84987C5.67932 4.2213 5.27205 5.16213 4.6339 5.80028C3.99575 6.43841 3.05492 6.84569 1.68349 7.00389C1.3889 7.03784 1.16669 7.28745 1.16699 7.58396C1.1673 7.88052 1.39002 8.12961 1.68467 8.16297C3.03291 8.31569 3.99517 8.72292 4.64954 9.36546C5.30035 10.0045 5.71535 10.944 5.83593 12.3017C5.86271 12.6029 6.11523 12.8337 6.41763 12.8334C6.72009 12.833 6.97209 12.6016 6.99817 12.3003C7.11367 10.9656 7.52836 10.005 8.18344 9.34982C8.83858 8.69474 9.79922 8.28005 11.1339 8.16455C11.4352 8.13847 11.6666 7.88647 11.667 7.58402C11.6673 7.28162 11.4365 7.02909 11.1353 7.00232C9.77758 6.88174 8.83812 6.46676 8.19908 5.81592C7.55653 5.16155 7.14931 4.19929 6.99659 2.85105Z"
              fill="currentColor"
            ></path>
          </svg>
          <span class="font-semibold text-indigo-500 group-hover:text-indigo-600">
            {{'PAC.Copilot.Generate' | translate: {Default: 'Generate'} }}
          </span>
        </div>
      }

      <button type="button" class="action-btn action-btn-m w-7 h-7"
        [class.text-primary-500]="editorOptions().wordWrap"
        (click)="toggleWrap()"
      >
        <i class="ri-text-wrap"></i>
      </button>
      
      @if (variables()?.length) {
        <button type="button" class="action-btn action-btn-m w-7 h-7"
          (click)="showSuggestions()"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4">
            <g id="variable-02"><g id="Vector"><path d="M13.9986 8.76189C14.6132 8.04115 15.5117 7.625 16.459 7.625H16.5486C17.1009 7.625 17.5486 8.07272 17.5486 8.625C17.5486 9.17728 17.1009 9.625 16.5486 9.625H16.459C16.0994 9.625 15.7564 9.78289 15.5205 10.0595L13.1804 12.8039L13.9213 15.4107C13.9372 15.4666 13.9859 15.5 14.0355 15.5H15.4296C15.9819 15.5 16.4296 15.9477 16.4296 16.5C16.4296 17.0523 15.9819 17.5 15.4296 17.5H14.0355C13.0858 17.5 12.2562 16.8674 11.9975 15.9575L11.621 14.6328L10.1457 16.3631C9.5311 17.0839 8.63257 17.5 7.68532 17.5H7.59564C7.04336 17.5 6.59564 17.0523 6.59564 16.5C6.59564 15.9477 7.04336 15.5 7.59564 15.5H7.68532C8.04487 15.5 8.38789 15.3421 8.62379 15.0655L10.964 12.3209L10.2231 9.71433C10.2072 9.65839 10.1586 9.625 10.1089 9.625H8.71484C8.16256 9.625 7.71484 9.17728 7.71484 8.625C7.71484 8.07272 8.16256 7.625 8.71484 7.625H10.1089C11.0586 7.625 11.8883 8.25756 12.1469 9.16754L12.5234 10.4921L13.9986 8.76189Z" fill="currentColor"></path><path d="M5.429 3C3.61372 3 2.143 4.47071 2.143 6.286V10.4428L1.29289 11.2929C1.10536 11.4804 1 11.7348 1 12C1 12.2652 1.10536 12.5196 1.29289 12.7071L2.143 13.5572V17.714C2.143 19.5293 3.61372 21 5.429 21C5.98128 21 6.429 20.5523 6.429 20C6.429 19.4477 5.98128 19 5.429 19C4.71828 19 4.143 18.4247 4.143 17.714V13.143C4.143 12.8778 4.03764 12.6234 3.85011 12.4359L3.41421 12L3.85011 11.5641C4.03764 11.3766 4.143 11.1222 4.143 10.857V6.286C4.143 5.57528 4.71828 5 5.429 5C5.98128 5 6.429 4.55228 6.429 4C6.429 3.44772 5.98128 3 5.429 3Z" fill="currentColor"></path><path d="M18.5708 3C18.0185 3 17.5708 3.44772 17.5708 4C17.5708 4.55228 18.0185 5 18.5708 5C19.2815 5 19.8568 5.57529 19.8568 6.286V10.857C19.8568 11.1222 19.9622 11.3766 20.1497 11.5641L20.5856 12L20.1497 12.4359C19.9622 12.6234 19.8568 12.8778 19.8568 13.143V17.714C19.8568 18.4244 19.2808 19 18.5708 19C18.0185 19 17.5708 19.4477 17.5708 20C17.5708 20.5523 18.0185 21 18.5708 21C20.3848 21 21.8568 19.5296 21.8568 17.714V13.5572L22.7069 12.7071C23.0974 12.3166 23.0974 11.6834 22.7069 11.2929L21.8568 10.4428V6.286C21.8568 4.47071 20.3861 3 18.5708 3Z" fill="currentColor"></path></g></g>
          </svg>
        </button>
      }

      <button type="button" class="action-btn action-btn-m danger w-7 h-7"
        (click)="remove()"
      >
        <i class="ri-delete-bin-2-line"></i>
      </button>

      <button type="button" class="action-btn action-btn-m w-7 h-7"
        (click)="copy()"
      >
        @if (copied()) {
          <i class="ri-clipboard-fill"></i>
        } @else {
          <i class="ri-clipboard-line"></i>
        }
      </button>

      <ng-content></ng-content>
    </div>
  </div>

  <div class="content gow relative overflow-auto">
    <div class="edit-container pt-2 -ml-4 rounded-t-xl text-sm overflow-hidden text-gray-700"
      [style.height.px]="height"
      >
      <ngx-monaco-editor class="!h-full w-full max-h-full"
        [options]="editorOptions()"
        [ngModel]="prompt()"
        (ngModelChange)="prompt.set($event);elementRef.nativeElement.blur()"
        (resized)="onResized()"
        (onInit)="onInit($event)"
      />
    </div>

    <div class="absolute bottom-0 left-0 w-full flex justify-center h-2 cursor-row-resize"
      (mousedown)="onMouseDown($event)">
      <div class="w-5 h-[3px] rounded-sm bg-gray-300"></div>
    </div>
  </div>
</div>

<ng-template #suggestionsTemplate>
  <xpert-variable-panel class="border-[0.5px] border-solid border-slate-200 rounded-xl shadow-lg bg-components-card-bg" (click)="$event.stopPropagation()" 
    [variables]="variables()"
    [ngModel]="null"
    (ngModelChange)="setVariable($event);hideSuggestions()"
    (close)="hideSuggestions()"
  />
</ng-template>