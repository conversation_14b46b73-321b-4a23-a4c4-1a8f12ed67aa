<div class="flex-1 mx-auto max-w-2xl">
  <form class="flex flex-wrap gap-4 p-4" [formGroup]="formGroup">
    <!-- <ngm-select class="flex-1" [label]="'PAC.Copilot.ExpertRole' | translate: {Default: 'Expert Role'}" valueKey="key" [displayBehaviour]="DisplayBehaviour.descriptionOnly"
      formControlName="role"
      [selectOptions]="roles()"
    /> -->
  
    <ngm-input class="w-1/2" [label]="'PAC.Copilot.Examples.Command' | translate: {Default: 'Command'}" valueKey="key"
      formControlName="command"
      [options]="commandOptions()"
    />
  
    <div class="flex-1 min-w-full flex flex-col">
      <label class="ngm-input-label shrink-0">{{'PAC.Copilot.Examples.Input' | translate: {Default: 'Input'} }}</label>
        <textarea class="ngm-input-element" matInput formControlName="input"
          cdkTextareaAutosize
          cdkAutosizeMinRows="1"
          cdkAutosizeMaxRows="5">
      </textarea>
    </div>

    <div class="flex-1 min-w-full flex flex-col">
      <label class="ngm-input-label shrink-0">{{'PAC.Copilot.Examples.Output' | translate: {Default: 'Output'} }}</label>
      <textarea class="ngm-input-element" matInput formControlName="output"
        cdkTextareaAutosize
        cdkAutosizeMinRows="2"
        cdkAutosizeMaxRows="8"
      ></textarea>
    </div>
  </form>
</div>

<div class="w-full sticky bottom-0 bg-gray-50">
  <div class="flex justify-between items-center p-4 mx-auto max-w-2xl">
    <div></div>
    <div ngmButtonGroup>
      <button mat-button (click)="close()">{{'PAC.KEY_WORDS.Cancel' | translate: {Default: 'Cancel'} }}</button>
      <button mat-raised-button color="primary" [disabled]="loading() || formGroup.invalid || formGroup.pristine"
        (click)="upsert()"
      >{{'PAC.KEY_WORDS.Save' | translate: {Default: 'Save'} }}</button>
    </div>
  </div>
</div>


@if (loading()) {
  <ngm-spin class="absolute left-0 top-0 w-full h-full flex justify-center items-center"></ngm-spin>
}