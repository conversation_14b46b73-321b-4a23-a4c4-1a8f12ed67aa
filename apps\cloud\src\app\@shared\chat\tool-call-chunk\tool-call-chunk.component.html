<button class="inline-flex items-center gap-1.5 py-1 px-2 text-sm hover:bg-white rounded-lg transition-colors cursor-pointer border border-neutral-200 dark:border-neutral-700/50">
  <chat-message-step-icon class="w-4 h-4 shrink-0 inline-flex justify-center items-center text-text-secondary"
    [step]="{
      type: chunk().type, 
      toolset: chunk().toolset, 
      toolsetId: chunk().toolset_id
    }"
  />
  <span class="text-sm max-w-[200px] truncate text-foreground" [title]="chunk().title">{{chunk().title}}</span>
    @if (chunk().status === 'fail') {
      <span class="font-mono text-xs ml-1 text-text-destructive truncate max-w-[200px]" [title]="chunk().error">{{chunk().error}}</span>
    } @else {
      <span class="font-mono text-xs ml-1 text-text-secondary truncate max-w-[200px]" [title]="chunk().message">{{chunk().message}}</span>
    }
  <span class="font-mono text-xs text-text-secondary ml-1">
    @if (conversationStatus() === 'busy' && chunk().status === 'running') {
      {{ chunk().created_date | relativeTimes:100 | number:'0.3-3' }}s
    } @else if (duration()) {
      {{ duration() }}s
    }
  </span>
</button>