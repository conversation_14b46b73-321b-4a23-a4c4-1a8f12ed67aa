<div class="flex-1 flex flex-col md:flex-row h-64">
    <div class="flex h-28 md:h-auto md:w-48 overflow-hidden">
        <img
            class="w-full rounded-t-lg object-cover md:h-auto md:w-auto md:rounded-none md:rounded-l-lg"
            [src]="thumbnail || '/assets/images/dashboard/template-02.jpg'"
            [alt]="story?.name"
            loading="lazy"/>
    </div>

    <div class="flex-1 flex flex-col justify-start p-4 overflow-hidden">
        <h5 class="shrink-0 mb-2 text-lg font-medium text-neutral-800 dark:text-neutral-50 overflow-hidden whitespace-nowrap text-ellipsis">
            <a [routerLink]="[storyLink ?? '/story', story.id]"
                [title]="story.name"
            ><span [ngmHighlight]="highlight" [content]="story.name" [caseSensitive]="false" customClasses="bg-transparent font-semibold text-amber-500"></span></a>
        </h5>

        <p class="text-sm text-neutral-500 dark:text-neutral-300 flex flex-col">
            <span class="overflow-hidden text-ellipsis">{{ story.updatedBy | createdBy }}</span>
            <span>
                {{ 'PAC.SHARED.StoryCard.LastUpdated' | translate: {Default: 'Last updated'} }} {{ updatedAt }}
            </span>
        </p>

        <div class="flex-1 overflow-y-auto">
            <p class="mb-4 text-sm text-neutral-600 dark:text-neutral-200">
                <span [ngmHighlight]="highlight" [content]="story.description" [caseSensitive]="false" customClasses="bg-transparent font-semibold text-amber-500"></span>
            </p>
        </div>

        <div class="flex justify-between items-center">
            <div class="flex items-center gap-2 text-xs">
                <ng-container *ngIf="story.pv">
                    <mat-icon fontSet="material-icons-outlined">play_circle</mat-icon>
                    <span>
                        {{story.pv | number}}
                    </span>
                </ng-container>
            </div>
            <a [routerLink]="[storyLink ?? '/story', story.id]" class="pac-story-card__button self-end inline-flex items-center px-3 py-2 text-sm font-medium text-center rounded-lg
                ring-offset-2 ring-offset-transparent ring ring-transparent focus:outline-none">
                {{ 'PAC.SHARED.StoryCard.Open' | translate: {Default: 'Open'} }}
                <svg aria-hidden="true" class="w-4 h-4 ml-2 -mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path></svg>
            </a>
        </div>
    </div>
</div>