@switch(type()) {
  @case(eParameterTypeEnum.STRING) {
    <div class="flex py-[7px]">
      <div class="flex items-center h-[18px] text-[13px] font-medium text-gray-900">
        @if (required()) {
          <span class="mr-1 text-red-500">*</span>
        }
        {{schema().label | i18n}}</div>
      @if (schema().description) {
        <div class="ml-0.5 w-5 h-5 flex justify-center items-center"
          [matTooltip]="schema().description | i18n"
          matTooltipPosition="above">
          <i class="ri-question-line text-text-quaternary hover:text-text-tertiary"></i>
        </div>
      }
    </div>
    <input class="flex h-9 w-full py-1 px-2 rounded-lg text-sm leading-normal bg-gray-100 caret-primary-600 hover:bg-gray-100 focus:ring-1 focus:ring-inset focus:ring-gray-200 focus-visible:outline-none focus:bg-gray-50 placeholder:text-gray-400" 
      [placeholder]="schema().placeholder | i18n" type="text" [(ngModel)]="value$">
  }
  @case(eParameterTypeEnum.NUMBER) {
    <div class="flex py-[7px]">
      <div class="flex items-center h-[18px] text-[13px] font-medium text-gray-900">
        @if (required()) {
          <span class="mr-1 text-red-500">*</span>
        }
        {{schema().label | i18n}}</div>
      @if (schema().description) {
        <div class="ml-0.5 w-5 h-5 flex justify-center items-center"
          [matTooltip]="schema().description | i18n"
          matTooltipPosition="above">
          <i class="ri-question-line text-text-quaternary hover:text-text-tertiary"></i>
        </div>
      }
    </div>
    <input class="flex h-9 w-full py-1 px-2 rounded-lg text-sm leading-normal bg-gray-100 caret-primary-600 hover:bg-gray-100 focus:ring-1 focus:ring-inset focus:ring-gray-200 focus-visible:outline-none focus:bg-gray-50 placeholder:text-gray-400" 
      [placeholder]="schema().placeholder | i18n" type="number" [(ngModel)]="value$">
  }
  @case(eParameterTypeEnum.SECRET_INPUT) {
    <div class="flex py-[7px]">
      <div class="flex items-center h-[18px] text-[13px] font-medium text-gray-900">
        @if (required()) {
          <span class="mr-1 text-red-500">*</span>
        }
        {{schema().label | i18n}}</div>
      @if (schema().description) {
        <div class="ml-0.5 w-5 h-5 flex justify-center items-center"
          [matTooltip]="schema().description | i18n"
          matTooltipPosition="above">
          <i class="ri-question-line text-text-quaternary hover:text-text-tertiary"></i>
        </div>
      }
    </div>
    <input class="flex h-9 w-full py-1 px-2 rounded-lg text-sm leading-normal bg-gray-100 caret-primary-600 hover:bg-gray-100 focus:ring-1 focus:ring-inset focus:ring-gray-200 focus-visible:outline-none focus:bg-gray-50 placeholder:text-gray-400" 
      [placeholder]="schema().placeholder | i18n" type="password" [(ngModel)]="value$">
  }
  @case(eParameterTypeEnum.SELECT) {
    <div class="flex py-[7px]">
      <div class="flex items-center h-[18px] text-[13px] font-medium text-gray-900">
        @if (required()) {
          <span class="mr-1 text-red-500">*</span>
        }
        {{schema().label | i18n}}</div>
      @if (schema().description) {
        <div class="ml-0.5 w-5 h-5 flex justify-center items-center"
          [matTooltip]="schema().description | i18n"
          matTooltipPosition="above">
          <i class="ri-question-line text-text-quaternary hover:text-text-tertiary"></i>
        </div>
      }
    </div>
    <ngm-select class="flex items-stretch h-9" [selectOptions]="schema().options" [placeholder]="schema().placeholder | i18n" [(ngModel)]="value$" />
  }
  @case(eParameterTypeEnum.BOOLEAN) {
    <div class="mb-2 mt-4 flex justify-start items-center">
      <ngm-checkbox [label]="schema().label | i18n" [(ngModel)]="value$" />
      @if (required()) {
        <span class="mx-1 text-red-500">*</span>
      }
      @if (schema().description) {
        <div class="ml-0.5 w-5 h-5 flex justify-center items-center"
          [matTooltip]="schema().description | i18n"
          matTooltipPosition="above">
          <i class="ri-question-line text-text-quaternary hover:text-text-tertiary"></i>
        </div>
      }
    </div>
  }
  <!-- Others: file and array -->
}

@if (help(); as help) {
  <a [href]="help.url | i18n" target="_blank" rel="noopener noreferrer" class="group inline-flex items-center text-xs text-primary-500 hover:text-primary-600 transition-all">
    {{help.title | i18n}}
    <i class="ri-external-link-line ml-1 group-hover:translate-x-2 transition-all"></i>
  </a>
}