:host {
  @apply flex flex-wrap items-center gap-2;

  .tag {
    &.blue {
      @apply text-blue-500;
    }
    &.green {
      @apply text-green-500;
    }
    &.red {
      @apply text-red-500;
    }
    &.yellow {
      @apply text-yellow-500;
    }
  }

  &.selectable {
    .tag {
      @apply cursor-pointer bg-gray-100/80;

      &.selected {
        @apply bg-gray-100;
      }

      &.blue.selected {
        @apply bg-blue-100 ;
      }
      &.green.selected {
        @apply bg-green-100 ;
      }
      &.red.selected {
        @apply bg-red-100 ;
      }
      &.yellow.selected {
        @apply bg-yellow-100;
      }
    }
  }

  &:not(.selectable) {
    .tag {
      &.blue {
        @apply bg-blue-100 ;
      }
      &.green {
        @apply bg-green-100 ;
      }
      &.red {
        @apply bg-red-100 ;
      }
      &.yellow {
        @apply bg-yellow-100 ;
      }
    }
  }
}
