{"name": "xpert.ai", "version": "3.0", "description": "", "license": "AGPL-3.0", "homepage": "https://mtda.cloud", "repository": {"type": "git", "url": "https://github.com/xpert-ai/xpert.git"}, "bugs": {"url": "https://github.com/xpert-ai/xpert/issues"}, "private": true, "author": {"name": "XpertAI Co. LTD", "email": "<EMAIL>", "url": "https://mtda.cloud"}, "keywords": ["<PERSON><PERSON>", "AI", "Agent", "Copilot", "BI"], "scripts": {"start": "nx serve", "build": "nx build", "test": "nx test", "build:all": "yarn rimraf dist && yarn nx build contracts && yarn nx build common && yarn nx build config && yarn nx build auth && yarn nx build server && yarn nx build adapter && yarn nx build analytics && yarn nx build api"}, "dependencies": {"@langchain/community": "0.3.27", "@langchain/core": "0.3.40", "@langchain/langgraph": "0.2.53", "@langchain/ollama": "0.1.5", "@langchain/openai": "0.4.2", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@swc/helpers": "~0.5.0", "idb-keyval": "^6.0.2", "langchain": "0.3.15", "money-clip": "^3.0.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "tslib": "^2.3.0"}, "devDependencies": {"@nestjs/schematics": "^10.0.1", "@nestjs/testing": "^10.0.2", "@nx/jest": "17.1.2", "@nx/js": "17.1.2", "@nx/nest": "17.1.2", "@nx/node": "17.1.2", "@nx/rollup": "17.1.2", "@nx/web": "17.1.2", "@nx/webpack": "17.1.2", "@nx/workspace": "17.1.2", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.51", "@swc/jest": "0.2.20", "@types/jest": "29.4.4", "@types/node": "18.7.1", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "eslint": "8.15.0", "eslint-config-prettier": "8.1.0", "jest": "29.4.3", "nx": "17.1.2", "rimraf": "^5.0.5", "ts-jest": "29.1.1", "ts-node": "10.9.1", "typescript": "5.2.2"}, "workspaces": ["apps/*", "packages/*"], "resolutions": {"@langchain/core": "0.3.40"}}