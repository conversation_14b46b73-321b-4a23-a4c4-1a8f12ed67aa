# 创建CNFS、StorageClass和Deployment、StatefulSet对象。
apiVersion: storage.alibabacloud.com/v1beta1
kind: ContainerNetworkFileSystem
metadata:
  name: cnfs-nas-filesystem
spec:
  description: "cnfs"
  type: nas
  reclaimPolicy: Retain # 只支持Retain策略，删除CNFS时并不会删除NAS文件系统。
  # parameters:
  #   encryptType: SSE-KMS # 可选参数，不指定表示对文件不使用NAS托管加密，指定SSE-KMS表示开启此功能。
  #   enableTrashCan: "true" # 可选参数，不指定表示不打开回收站功能，指定true表示开启此功能。
