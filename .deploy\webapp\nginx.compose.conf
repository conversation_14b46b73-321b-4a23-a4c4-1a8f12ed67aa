user  nginx;
error_log  /var/log/nginx/error.log warn;
pid        /var/run/nginx.pid;

events {
  worker_connections 1024;
}

http {
  include /etc/nginx/mime.types;
  
  log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

  access_log  /var/log/nginx/access.log  main;

  #gzip  on;

  upstream api {
    server ${API_HOST}:${API_PORT};
  }

  server {
    listen              80;

    location / {
      root /srv/pangolin;
      try_files $uri $uri/ /index.html;
    }

    location /api/ {
      proxy_pass http://api;
      proxy_set_header Host $http_host;
      proxy_connect_timeout       5s;
      proxy_read_timeout          600s;
    }

    location /public/ {
      proxy_pass http://api;
      proxy_set_header Host $http_host;
      proxy_connect_timeout       5s;
      proxy_read_timeout          30s;
    }
  }
}
