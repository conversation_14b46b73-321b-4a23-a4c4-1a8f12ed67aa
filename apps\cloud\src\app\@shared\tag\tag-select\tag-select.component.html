<div id="tags-label" class="flex items-center gap-1 px-2 h-9 rounded-lg border-[0.5px] border-transparent bg-gray-100 cursor-pointer hover:bg-gray-200"
  [floatUi]="panel"
  [showTrigger]="eNgxFloatUiTriggers.click"
  [placement]="eNgxFloatUiPlacements.TOP"
>
  <div title="" class="grow space-x-1 text-[13px] leading-[18px] text-gray-700 truncate">
    @for (tag of selectedTags(); track tag.id) {
      <tag [tag]="tag" sm class="inline-block" />
    } @empty {
      {{ 'PAC.KEY_WORDS.ChooseTags' | translate: {Default: 'Choose tags'} }}
      @if (optional()) {
        ({{ 'PAC.KEY_WORDS.Optional' | translate: {Default: 'optional'} }})
      }
    }
  </div>
  <div class="shrink-0 ml-1 text-gray-700 opacity-60">
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="remixicon h-4 w-4">
      <path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"></path>
    </svg>
  </div>
</div>

<float-ui-content #panel>
  <div class="w-full relative bg-white rounded-lg border-[0.5px] border-gray-200 shadow-md">
    <div class="p-2 border-b-[0.5px] border-black/5">
      <div class="group flex items-center px-2 h-8 rounded-lg border border-transparent overflow-hidden shadow-xs">
        <div class="pointer-events-none shrink-0 flex items-center mr-1.5 justify-center w-4 h-4">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" aria-hidden="true" class="remixicon h-3.5 w-3.5 text-gray-500">
            <path d="M18.031 16.6168L22.3137 20.8995L20.8995 22.3137L16.6168 18.031C15.0769 19.263 13.124 20 11 20C6.032 20 2 15.968 2 11C2 6.032 6.032 2 11 2C15.968 2 20 6.032 20 11C20 13.124 19.263 15.0769 18.031 16.6168ZM16.0247 15.8748C17.2475 14.6146 18 12.8956 18 11C18 7.1325 14.8675 4 11 4C7.1325 4 4 7.1325 4 11C4 14.8675 7.1325 18 11 18C12.8956 18 14.6146 17.2475 15.8748 16.0247L16.0247 15.8748Z"></path>
          </svg>
        </div>
        <input class="grow block h-[18px] border-0 text-gray-700 text-[13px] placeholder:text-gray-500 appearance-none outline-none  caret-blue-600 !bg-white hover:!bg-white group-hover:!bg-white"
          [placeholder]="'PAC.KEY_WORDS.Search' | translate: {Default: 'Search'}" autocomplete="off" type="text" name="query" tabindex="-1"
          [formControl]="searchControl">
      </div>
    </div>
    <ul class="p-1 max-h-[264px] overflow-y-auto" cdkListbox cdkListboxMultiple
      [cdkListboxValue]="selectedTags()"
      [cdkListboxCompareWith]="listboxCompareWith"
      (cdkListboxValueChange)="selectTags($event)"
      aria-labelledby="tags-label"
      >
      @for (tag of tags$(); track tag.id) {
        <li class="flex items-center gap-2 pl-3 py-[6px] pr-2 rounded-lg cursor-pointer hover:bg-gray-100"
          [cdkOption]="tag"
          #option="cdkOption">
          <input type="checkbox" class="checkbox opacity-0 w-4 h-4 border border-gray-300 rounded bg-gray-50 focus:ring-3 focus:ring-blue-300 dark:bg-gray-700 dark:border-gray-600 dark:focus:ring-blue-600 dark:ring-offset-gray-800"
            checked
            [ngClass]="{'opacity-100': checkedWith(tag)}"
            >
          <div [title]="tag.description | i18n" class="grow text-sm text-gray-700 leading-5 truncate">
            <span [ngmHighlight]="searchControl.value" [content]="(tag.label | i18n) || tag.name"></span>
          </div>
        </li>
      }
    </ul>
  </div>
</float-ui-content>