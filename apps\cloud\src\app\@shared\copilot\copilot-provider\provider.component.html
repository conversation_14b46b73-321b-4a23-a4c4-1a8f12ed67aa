<div class="group flex pl-3 py-2 pr-2 rounded-t-xl">
  <div class="grow px-1 pt-1 pb-0.5">
    @if (icon()) {
      <div class="flex items-center">
        <img
          alt="provider-icon"
          [src]="icon() | i18n"
          class="w-auto h-8 mb-2"
        />
      </div>
    } @else {
      <div class="flex items-center">
        {{ label() | i18n }}
      </div>
    }
    
    <div class="flex gap-0.5">
      @for (modelType of supported_model_types(); track modelType) {
        <div class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium uppercase text-gray-500 cursor-default">
          {{modelType}}
        </div>
      }
    </div>
  </div>

  <div class="flex items-start">
    @if (!readonly()) {
      <button class="btn btn-small btn-danger h8 rounded-md items-center justify-center cursor-pointer z-20 hidden group-hover:flex"
        (click)="delete()">
        <i class="ri-delete-bin-line"></i>
      </button>
    }
  </div>

  @if (usage(); as usage) {
    <div class="group relative shrink-0 min-w-[112px] px-3 py-2 rounded-lg bg-white/[0.3] border-[0.5px] border-black/5 text-gray-500">
      <div class="flex items-center mb-2 h-4 text-sm font-medium text-gray-500">
        {{ 'PAC.Copilot.Quota' | translate: {Default: 'Quota'} }}
        <div class="p-[1px] w-5 h-5 shrink-0"
          [matTooltip]="'PAC.Copilot.FreeTrialTokenQuota' | translate: {Default: 'The free trial token quota becomes invalid after the organization\'s custom copilot is enabled.'}"
          matTooltipPosition="above"
          >
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
            class="text-text-quaternary hover:text-text-tertiary w-full h-full">
            <path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 15H13V17H11V15ZM13 13.3551V14H11V12.5C11 11.9477 11.4477 11.5 12 11.5C12.8284 11.5 13.5 10.8284 13.5 10C13.5 9.17157 12.8284 8.5 12 8.5C11.2723 8.5 10.6656 9.01823 10.5288 9.70577L8.56731 9.31346C8.88637 7.70919 10.302 6.5 12 6.5C13.933 6.5 15.5 8.067 15.5 10C15.5 11.5855 14.4457 12.9248 13 13.3551Z"></path>
          </svg>
        </div>
      </div>
      <div class="flex items-center h-4 text-sm text-gray-500">
        @if (usage.tokenLimit) {
          <span class="mr-0.5 text-sm font-semibold text-gray-700">{{usage.tokenLimit | number}}</span>
        } @else {
          <span class="mr-0.5 text-sm font-semibold text-gray-700">∞</span>
        }
        {{ 'PAC.Copilot.Tokens' | translate: {Default: 'Tokens'} }}
      </div>
      <div class="text-xs"
        [ngClass]="{
          'text-yellow-500': usageWarn(),
          'text-red-500': usageError(),
        }"
      >{{ 'PAC.Copilot.Remain' | translate: {Default: 'Remain'} }}: 
        @if (usage.tokenLimit) {
          {{ tokenRemain() | number: '0.0-0' }}
        } @else {
          100
        }
        %</div>
    </div>
  }

  @if (provider_credential_schema()) {
    <div class="shrink-0 relative flex flex-col justify-between ml-1 p-1 w-[112px] rounded-lg bg-white/[0.3] border-[0.5px] border-black/5">
      <div class="relative flex items-center justify-between mb-1 pt-1 pl-2 pr-[7px] h-5 text-sm font-medium text-gray-500">
        API-KEY
        <div class="w-2 h-2 border border-solid rounded-[3px] bg-[#31C48D] border-[#0E9F6E] shadow-[0_0_5px_-3px_rgba(14,159,110,0.1),0.5px_0.5px_3px_rgba(14,159,110,0.3),inset_1.5px_1.5px_0px_rgba(255,255,255,0.2)]"></div>
      </div>

      <div class="flex items-center gap-0.5">
        <button type="button" class="btn disabled:btn-disabled btn-secondary btn-small grow items-center"
          [disabled]="readonly()"
          (click)="openSetup()"
        >
          <i class="ri-settings-5-line mr-1"></i>
          {{'PAC.Copilot.Setup' | translate: {Default: 'Setup'} }}
        </button>
      </div>
    </div>
  }
</div>

@if (isShowModels()) {
  <div class="px-2 pb-2 rounded-b-xl">
    <div class="py-1 bg-white rounded-lg">
      <div class="flex items-center pl-1 pr-[3px]">
        <span class="group shrink-0 flex items-center mr-2">
          <span class="group-hover:hidden pl-1 pr-1.5 h-6 leading-6 text-sm font-medium text-gray-500">
            {{modelCount()}} {{'PAC.Copilot.Models' | translate: {Default: 'Models'} }}
          </span>
          <span class="hidden group-hover:inline-flex items-center pl-1 pr-1.5 h-6 text-sm font-medium text-gray-500 bg-gray-50 cursor-pointer rounded-lg"
            (click)="toggleShowModels()"
          >
            <i class="ri-arrow-up-double-line"></i>
            {{'PAC.Copilot.Collapse' | translate: {Default: 'Collapse'} }}
          </span>
        </span>
        @if (canCustomizableModel()) {
          <div class="grow flex justify-end">
            @if (!readonly()) {
              <span class="shrink-0 flex items-center px-1.5 h-6 text-sm font-medium text-gray-500 cursor-pointer hover:bg-primary-50 hover:text-primary-600 rounded-md undefined"
                (click)="addModel()"
              >
                <i class="ri-add-circle-line"></i>
                {{'PAC.Copilot.AddModel' | translate: {Default: 'Add model'} }}
              </span>
            }
          </div>
        }
      </div>

      @for (model of customModels(); track model.id) {
        <div class="group flex items-center pl-2 pr-2.5 h-8 rounded-lg hover:bg-gray-50">
          @if (smallIcon()) {
            <img alt="model-icon" class="w-4 h-4 shrink-0 mr-2"
              [src]="smallIcon() | i18n"
            />
          }

          <div class="flex items-center truncate text-[13px] grow text-sm font-normal text-gray-900">
            <div class="truncate" [title]="model.modelName">{{model.modelName}}</div>

            <div class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium uppercase text-gray-500 cursor-default ml-1">
              {{model.modelType}}
            </div>

            @if (model.modelProperties?.context_size) {
              <div class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium text-gray-500 cursor-default ml-1">
                {{(model.modelProperties.context_size / 1000) | number: '0.0-0'}}K
              </div>
            }
          </div>

          <div class="shrink-0 flex items-center">
            @if (!readonly()) {
              <button type="button" class="btn disabled:btn-disabled btn-secondary btn-small hidden group-hover:flex h-7 mr-2 items-center"
                (click)="editModel(model)">
                <i class="ri-settings-5-line mr-1"></i>
                {{'PAC.Copilot.Configure' | translate: {Default: 'Configure'} }}
              </button>
            }
          </div>
        </div>
      }

      @for (model of builtinModels(); track model.model) {
        <div class="group flex items-center pl-2 pr-2.5 h-8 rounded-lg hover:bg-gray-50"
          [ngClass]="model.deprecated ? 'opacity-60' : ''"
          [matTooltip]="model.deprecated ? ('PAC.Copilot.Deprecated' | translate: {Default: 'Deprecated'}) : ''"
          matTooltipPosition="before"
        >
          <img
            alt="model-icon"
            [src]="smallIcon() | i18n"
            class="w-4 h-4 shrink-0 mr-2"
          />

          <div
            class="flex items-center truncate text-base grow font-normal text-gray-900"
          >
            <div class="truncate" [title]="model.model">{{model.model}}</div>
            
            <div class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium uppercase text-gray-500 cursor-default ml-1">
              {{model.model_type}}
            </div>
            @if (model.model_properties?.mode) {
              <div class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium uppercase text-gray-500 cursor-default ml-1">
                {{model.model_properties?.mode}}
              </div>
            }
            @if (model.model_properties?.context_size) {
              <div
                class="flex items-center px-1 h-[18px] rounded-[5px] border border-black/8 bg-white/[0.48] text-[10px] font-medium text-gray-500 cursor-default ml-1"
              >
                {{(model.model_properties.context_size / 1000) | number: '0.0-0'}}K
              </div>
            }
          </div>
        </div>
      }
      
    </div>
  </div>
} @else {
  <div class="group flex items-center justify-between pl-2 py-1.5 pr-[11px] border-t border-t-black/5 bg-white/30 text-sm font-medium text-gray-500">
    <div class="group-hover:hidden pl-1 pr-1.5 h-6 leading-6">{{'PAC.Copilot.ShowNModels' | translate: {value: (modelCount() || ''), Default: 'Show ' + (modelCount() || '') + ' Models'} }}</div>
    <div class="hidden group-hover:flex items-center pl-1 pr-1.5 h-6 rounded-lg hover:bg-white cursor-pointer"
      (click)="toggleShowModels()"
    >
      <i class="ri-arrow-down-double-line"></i>
      {{'PAC.Copilot.ShowNModels' | translate: {value: (modelCount() || ''), Default: 'Show ' + (modelCount() || '') + ' Models'} }}
    </div>

    @if (!readonly() && canCustomizableModel()) {
      <span class="shrink-0 items-center px-1.5 h-6 text-sm font-medium text-gray-500 cursor-pointer hover:bg-primary-50 hover:text-primary-600 rounded-md hidden group-hover:flex group-hover:text-primary-600"
        (click)="addModel()"
      >
        <i class="ri-add-circle-line"></i>
        {{'PAC.Copilot.AddModel' | translate: {Default: 'Add model'} }}
      </span>
    }
  </div>
}

@if (loading()) {
  <ngm-spin class="absolute top-0 left-0 w-full h-full" />
}