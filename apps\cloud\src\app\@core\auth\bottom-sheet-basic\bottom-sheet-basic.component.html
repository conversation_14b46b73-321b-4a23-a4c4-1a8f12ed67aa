<h3 class="mb-4 text-lg">
  {{ 'PAC.SHARED.AuthBottomSheet.Title' | translate: {Default: "Provide an account for '" + data.name + "'", name: data.name} }}
</h3>

<form [formGroup]="form" (ngSubmit)="onSubmit()" class="flex flex-col justify-start items-stretch">

  <ngm-input [label]="'PAC.KEY_WORDS.Username' | translate: {Default: 'Username'}"
    formControlName="username"
  />
  
  <ngm-input [label]="'PAC.KEY_WORDS.Password' | translate: {Default: 'Password'}"
    type="password"
    formControlName="password"
  />

  <!-- 目前没有意义，因为 Auth 必须保存到 Server 才能被使用
  <mat-checkbox formControlName="remeberMe">
    {{ 'PAC.KEY_WORDS.RemeberMe' | translate: {Default: 'Remeber Me'} }}
  </mat-checkbox> -->

  <div class="flex justify-end items-center">
    <div ngmButtonGroup displayDensity="cosy">
      <button mat-flat-button type="button" displayDensity="cosy" (click)="onCancel()">
        {{ 'PAC.KEY_WORDS.Cancel' | translate: {Default: 'Cancel'} }}
      </button>
      <button mat-raised-button color="primary" displayDensity="cosy">
        {{ 'PAC.KEY_WORDS.Confirm' | translate: {Default: 'Confirm'} }}
      </button>
    </div>
  </div>
</form>
