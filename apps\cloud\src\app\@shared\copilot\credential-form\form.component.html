<form [formGroup]="formGroup">
  @for (credential of credentials(); track credential) {
    <div class="py-3">
      <div class="flex items-center py-2 text-sm text-gray-900">
        {{credential.label | i18n}}
        @if (credential.required) {
          <span class="ml-1 text-red-500">*</span>
        }
      </div>
      @switch (credential.type) {
        @case ('secret-input') {
          <div class="relative">
            <input
              class="block px-3 w-full h-9 bg-gray-100 text-sm rounded-lg border border-transparent appearance-none outline-none caret-primary-600 hover:border-[rgba(0,0,0,0.08)] hover:bg-gray-50 focus:bg-white focus:border-gray-300 focus:shadow-xs placeholder:text-sm placeholder:text-gray-400 false false"
              [placeholder]="credential.placeholder | i18n"
              type="password"
              [formControlName]="credential.variable"
            />
          </div>
        }
        @case ('text-input') {
          <div class="relative">
            <input
              class="block px-3 w-full h-9 bg-gray-100 text-sm rounded-lg border border-transparent appearance-none outline-none caret-primary-600 hover:border-[rgba(0,0,0,0.08)] hover:bg-gray-50 focus:bg-white focus:border-gray-300 focus:shadow-xs placeholder:text-sm placeholder:text-gray-400 false false"
              [placeholder]="credential.placeholder | i18n"
              type="text"
              [formControlName]="credential.variable"
            />
          </div>
        }
        @case ('select') {
          <ngm-select class=""
            [placeholder]="credential.placeholder | i18n"
            [selectOptions]="credential.options"
            [formControlName]="credential.variable" />
        }
        @case ('radio') {
          <mat-radio-group [formControlName]="credential.variable">
            @for (option of credential.options; track option) {
              <mat-radio-button [value]="option.value">{{option.label | i18n}}</mat-radio-button>
            }
          </mat-radio-group>
        }
        @case (eParameterType.BOOLEAN) {
          <mat-slide-toggle [formControlName]="credential.variable"
            ngm-density="xs"
          />
        }
      }
    </div>
  }
</form>