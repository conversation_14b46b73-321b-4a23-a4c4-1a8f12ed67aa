:host {
  @apply block pt-2;
}

.tool-row.disabled {
  @apply bg-gray-50 text-text-secondary;
}

thead tr {
  @apply text-left;

  th {
    @apply px-4;
  }
}

.cdk-drag-preview {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
  @apply bg-components-card-bg;
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
}

.cdk-drop-list-dragging .tool-row:not(.cdk-drag-placeholder) {
  transition: transform 100ms cubic-bezier(0, 0, 0.2, 1);
}