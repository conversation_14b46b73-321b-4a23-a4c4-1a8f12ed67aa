# apiVersion: v1
# kind: Service
# metadata:
#   name: postgres
# spec:
#   type: ClusterIP
#   selector:
#     app: postgres
#   ports:
#     - protocol: TCP
#       port: 5432
# ---
# apiVersion: v1
# kind: Service
# metadata:
#   name: db-adminer
# spec:
#   type: ClusterIP
#   selector:
#     app: db-adminer
#   ports:
#     - protocol: TCP
#       port: 8084
#       targetPort: 8080
# ---
# apiVersion: v1
# kind: Service
# metadata:
#   name: redis
# spec:
#   type: ClusterIP
#   selector:
#     app: redis
#   ports:
#     - protocol: TCP
#       name: redis
#       port: 6379
#       targetPort: 6379
#     - protocol: TCP
#       name: insight
#       port: 8001
#       targetPort: 8001
# ---
# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: postgres
# spec:
#   strategy:
#     rollingUpdate:
#       maxSurge: 1
#       maxUnavailable: 1
#     type: RollingUpdate
#   replicas: 1
#   selector:
#     matchLabels:
#       app: postgres
#   template:
#     metadata:
#       labels:
#         app: postgres
#     spec:
#       containers:
#         - name: postgres
#           image: registry.cn-hangzhou.aliyuncs.com/metad/pgvector:pg12
#           # imagePullPolicy: Always
#           ports:
#             - containerPort: 5432
#           env:
#             - name: POSTGRES_USER
#               valueFrom:
#                 secretKeyRef:
#                   name: ocap-secrets
#                   key: DB_USER
#                   optional: false
#             - name: POSTGRES_PASSWORD
#               valueFrom:
#                 secretKeyRef:
#                   name: ocap-secrets
#                   key: DB_PASS
#                   optional: false
#             - name: POSTGRES_DB
#               valueFrom:
#                 secretKeyRef:
#                   name: ocap-secrets
#                   key: DB_NAME
#                   optional: false
#             - name: PGDATA
#               value: /var/lib/postgresql/data/pgdata
#           volumeMounts:
#             - mountPath: /var/lib/postgresql/data
#               name: postgredb
#       volumes:
#         - name: postgredb
#           persistentVolumeClaim:
#             claimName: db-pvc
# ---
# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: db-adminer
# spec:
#   strategy:
#     rollingUpdate:
#       maxSurge: 1
#       maxUnavailable: 1
#     type: RollingUpdate
#   replicas: 1
#   selector:
#     matchLabels:
#       app: db-adminer
#   template:
#     metadata:
#       labels:
#         app: db-adminer
#     spec:
#       containers:
#         - name: adminer
#           image: adminer:latest
#           ports:
#             - containerPort: 8080
# ---
# apiVersion: apps/v1
# kind: Deployment
# metadata:
#   name: redis
# spec:
#   strategy:
#     rollingUpdate:
#       maxSurge: 1
#       maxUnavailable: 1
#     type: RollingUpdate
#   replicas: 1
#   selector:
#     matchLabels:
#       app: redis
#   template:
#     metadata:
#       labels:
#         app: redis
#     spec:
#       containers:
#         - name: redis
#           image: redis/redis-stack:latest
#           # imagePullPolicy: Always
#           ports:
#             - containerPort: 6379
#             - containerPort: 8001
#           env:
#             - name: REDIS_PASSWORD
#               valueFrom:
#                 secretKeyRef:
#                   name: ocap-secrets
#                   key: REDIS_PASSWORD
#           command:
#             - redis-server
#           args:
#             - --requirepass
#             - $(REDIS_PASSWORD)
