kind: PersistentVolumeClaim
apiVersion: v1
metadata:
  name: db-pvc
spec:
  accessModes:
    - ReadWriteMany
  storageClassName: alibabacloud-cnfs-nas
  volumeName: prod-db-pv
  resources:
    requests:
      storage: 10Gi # 如果打开目录限额功能，则storage字段会生效，动态创建目录写入数据量最大为 10 GiB。
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: prod-db-pv
  labels:
    type: nas
spec:
  storageClassName: alibabacloud-cnfs-nas
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  hostPath:
    path: "/prod-db/"