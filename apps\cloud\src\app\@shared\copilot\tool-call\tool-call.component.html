<div class="flex items-center gap-1 mb-1 system-sm-semibold-uppercase text-text-secondary">
  <span>{{ 'PAC.KEY_WORDS.Tool' | translate: {Default: 'Tool'} }}</span>
  <span class="font-semibold truncate" [title]="toolCall().name">{{toolCall().name}}</span>
</div>
<div class="mb-2 overflow-auto system-xs-regular text-text-tertiary">
  @if (args()) {
    <ngx-json-viewer [json]="args()" [depth]="1" />
  }
</div>