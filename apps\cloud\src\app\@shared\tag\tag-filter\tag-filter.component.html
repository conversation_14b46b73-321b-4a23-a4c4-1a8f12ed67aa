<div class="flex items-center gap-1 px-2 h-8 rounded-lg border-[0.5px] border-transparent bg-gray-200 cursor-pointer hover:bg-gray-300"
  #menuTrigger
  [cdkMenuTriggerFor]="tagsMenu"
  [cdkMenuPosition]="[
  {
    originX: 'end',
    originY: 'bottom',
    overlayX: 'end',
    overlayY: 'top',
    offsetX: 3,
  }
 ]"
>
  <div class="p-[1px]">
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-700">
      <g id="Icon" clip-path="url(#clip0_17795_9693)">
        <path id="Icon_2" d="M4.66699 4.6665H4.67283M1.16699 3.03317L1.16699 5.6433C1.16699 5.92866 1.16699 6.07134 1.19923 6.20561C1.22781 6.32465 1.27495 6.43845 1.33891 6.54284C1.41106 6.66057 1.51195 6.76146 1.71373 6.96324L6.18709 11.4366C6.88012 12.1296 7.22664 12.4761 7.62621 12.606C7.97769 12.7202 8.35629 12.7202 8.70777 12.606C9.10735 12.4761 9.45386 12.1296 10.1469 11.4366L11.4371 10.1464C12.1301 9.45337 12.4766 9.10686 12.6065 8.70728C12.7207 8.35581 12.7207 7.9772 12.6065 7.62572C12.4766 7.22615 12.1301 6.87963 11.4371 6.1866L6.96372 1.71324C6.76195 1.51146 6.66106 1.41057 6.54332 1.33842C6.43894 1.27446 6.32514 1.22732 6.20609 1.19874C6.07183 1.1665 5.92915 1.1665 5.64379 1.1665L3.03366 1.1665C2.38026 1.1665 2.05357 1.1665 1.804 1.29366C1.58448 1.40552 1.406 1.58399 1.29415 1.80352C1.16699 2.05308 1.16699 2.37978 1.16699 3.03317ZM4.95866 4.6665C4.95866 4.82759 4.82808 4.95817 4.66699 4.95817C4.50591 4.95817 4.37533 4.82759 4.37533 4.6665C4.37533 4.50542 4.50591 4.37484 4.66699 4.37484C4.82808 4.37484 4.95866 4.50542 4.95866 4.6665Z" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
      </g>
      <defs><clipPath id="clip0_17795_9693"><rect width="14" height="14" fill="white"></rect></clipPath></defs>
    </svg>
  </div>
  
  <div class="max-w-[120px] truncate text-[13px] leading-[18px] text-gray-700">
    @for (tag of top2Tags(); track tag; let last = $last) {
      {{tag.label | i18n}}
      @if (!last) {
        ,
      }
    } @empty {
      {{'PAC.KEY_WORDS.AllTags' | translate: {Default: 'All Tags'} }}
    }
    @if (restTags().length) {
      +{{restTags().length}}
    }
  </div>

  @if (tags().length) {
    <div class="p-[1px] cursor-pointer group/clear" (click)="clear()">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-3.5 w-3.5 text-gray-400 group-hover/clear:text-gray-600" data-icon="XCircle" aria-hidden="true"><path id="Solid" fill-rule="evenodd" clip-rule="evenodd" d="M8.00008 0.666016C3.94999 0.666016 0.666748 3.94926 0.666748 7.99935C0.666748 12.0494 3.94999 15.3327 8.00008 15.3327C12.0502 15.3327 15.3334 12.0494 15.3334 7.99935C15.3334 3.94926 12.0502 0.666016 8.00008 0.666016ZM10.4715 5.52794C10.7318 5.78829 10.7318 6.2104 10.4715 6.47075L8.94289 7.99935L10.4715 9.52794C10.7318 9.78829 10.7318 10.2104 10.4715 10.4708C10.2111 10.7311 9.78903 10.7311 9.52868 10.4708L8.00008 8.94216L6.47149 10.4708C6.21114 10.7311 5.78903 10.7311 5.52868 10.4708C5.26833 10.2104 5.26833 9.78829 5.52868 9.52794L7.05727 7.99935L5.52868 6.47075C5.26833 6.2104 5.26833 5.78829 5.52868 5.52794C5.78903 5.26759 6.21114 5.26759 6.47149 5.52794L8.00008 7.05654L9.52868 5.52794C9.78903 5.26759 10.2111 5.26759 10.4715 5.52794Z" fill="currentColor"></path></svg></div>
  } @else {
    <div class="p-[1px]">
      <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="remixicon h-3.5 w-3.5 text-gray-700">
        <path d="M11.9999 13.1714L16.9497 8.22168L18.3639 9.63589L11.9999 15.9999L5.63599 9.63589L7.0502 8.22168L11.9999 13.1714Z"></path>
      </svg>
    </div>
  }
</div>

<ng-template #tagsMenu>
  <div cdkMenu class="p-2 w-full" [style.min-width.px]="menuTrigger.offsetWidth">
    <ul cdkListbox [(ngModel)]="tags" [cdkListboxCompareWith]="compareWith" cdkListboxMultiple
      (ngModelChange)="onTagChange()"
    >
      @for (tag of allTags(); track tag.id) {
        <div #option="cdkOption" [cdkOption]="tag" class="flex items-center px-2 py-1 cursor-pointer rounded-lg hover:bg-hover-bg">
          @if (tag.icon) {
            <div [innerHTML]="tag.icon | safe: 'html'" class="shrink-0 w-4 h-4 mr-2"></div>
          }
          <div class="flex-1 w-24">{{(tag.label | i18n) || tag.description}}</div>
        @if (option.isSelected()) {
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"
             class="shrink-0 w-4 h-4 text-primary-600">
            <g id="check">
              <path id="Icon" d="M13.3334 4L6.00008 11.3333L2.66675 8" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
            </g>
          </svg>
        }
        </div>
      } @empty {
        <div class="text-text-secondary">{{ 'PAC.KEY_WORDS.Empty' | translate: {Default: 'Empty'} }}</div>
      }
    </ul>
  </div>
</ng-template>