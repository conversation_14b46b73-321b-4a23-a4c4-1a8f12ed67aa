<div class="h-full flex flex-col col-span-1 border-[0.5px] border-black/5 rounded-xl min-h-[160px] transition-all duration-200 ease-in-out cursor-pointer hover:shadow-lg
              bg-gray-200 dark:bg-neutral-800 hover:bg-gray-50 dark:hover:bg-neutral-600">
  <div class="group grow rounded-t-xl hover:bg-white dark:hover:bg-neutral-700"
    (click)="onCreate()">
      <div class="shrink-0 flex items-center p-4 pb-3">
        <div class="w-10 h-10 flex items-center justify-center border
            border-gray-200 bg-gray-100 rounded-lg group-hover:border-primary-100 group-hover:bg-primary-50
            dark:border-neutral-600 dark:bg-neutral-700 dark:group-hover:bg-primary-200/20 dark:group-hover:border-primary-100/20">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor"
            class="remixicon w-4 h-4 text-gray-500 group-hover:text-primary-600">
            <path d="M11 11V5H13V11H19V13H13V19H11V13H5V11H11Z"></path>
          </svg>
        </div>
        
        <div class="ml-3 text-sm font-semibold leading-5 text-gray-800 group-hover:text-primary-600 dark:text-gray-200">{{title()}}</div>
    </div>

    @if (description()) {
      <div class="p-4 pt-0 text-text-tertiary text-sm font-thin">{{description()}}</div>
    }

    <ng-content></ng-content>
  </div>
    
  <div class="px-4 py-3 rounded-b-xl border-t-[0.5px]
        border-black/5 text-gray-500 hover:text-[#155EEF] hover:bg-white dark:hover:bg-neutral-500">
    @if (helpUrl()) {
      <a [href]="helpWebsite() + helpUrl()" target="_blank" rel="noopener noreferrer" class="flex items-center space-x-1">
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="shrink-0 w-3 h-3" data-icon="BookOpen01" aria-hidden="true">
          <g id="book-open-01"><path id="Fill" opacity="0.12" d="M1 3.1C1 2.53995 1 2.25992 1.10899 2.04601C1.20487 1.85785 1.35785 1.70487 1.54601 1.60899C1.75992 1.5 2.03995 1.5 2.6 1.5H2.8C3.9201 1.5 4.48016 1.5 4.90798 1.71799C5.28431 1.90973 5.59027 2.21569 5.78201 2.59202C6 3.01984 6 3.5799 6 4.7V10.5L5.94997 10.425C5.60265 9.90398 5.42899 9.64349 5.19955 9.45491C4.99643 9.28796 4.76238 9.1627 4.5108 9.0863C4.22663 9 3.91355 9 3.28741 9H2.6C2.03995 9 1.75992 9 1.54601 8.89101C1.35785 8.79513 1.20487 8.64215 1.10899 8.45399C1 8.24008 1 7.96005 1 7.4V3.1Z" fill="currentColor"></path><path id="Icon" d="M6 10.5L5.94997 10.425C5.60265 9.90398 5.42899 9.64349 5.19955 9.45491C4.99643 9.28796 4.76238 9.1627 4.5108 9.0863C4.22663 9 3.91355 9 3.28741 9H2.6C2.03995 9 1.75992 9 1.54601 8.89101C1.35785 8.79513 1.20487 8.64215 1.10899 8.45399C1 8.24008 1 7.96005 1 7.4V3.1C1 2.53995 1 2.25992 1.10899 2.04601C1.20487 1.85785 1.35785 1.70487 1.54601 1.60899C1.75992 1.5 2.03995 1.5 2.6 1.5H2.8C3.9201 1.5 4.48016 1.5 4.90798 1.71799C5.28431 1.90973 5.59027 2.21569 5.78201 2.59202C6 3.01984 6 3.5799 6 4.7M6 10.5V4.7M6 10.5L6.05003 10.425C6.39735 9.90398 6.57101 9.64349 6.80045 9.45491C7.00357 9.28796 7.23762 9.1627 7.4892 9.0863C7.77337 9 8.08645 9 8.71259 9H9.4C9.96005 9 10.2401 9 10.454 8.89101C10.6422 8.79513 10.7951 8.64215 10.891 8.45399C11 8.24008 11 7.96005 11 7.4V3.1C11 2.53995 11 2.25992 10.891 2.04601C10.7951 1.85785 10.6422 1.70487 10.454 1.60899C10.2401 1.5 9.96005 1.5 9.4 1.5H9.2C8.07989 1.5 7.51984 1.5 7.09202 1.71799C6.71569 1.90973 6.40973 2.21569 6.21799 2.59202C6 3.01984 6 3.5799 6 4.7" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path></g>
        </svg>
        <div class="grow leading-[18px] text-xs font-normal truncate" [title]="helpTitle()">{{helpTitle()}}</div>
        <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg" class="shrink-0 w-3 h-3" data-icon="ArrowUpRight" aria-hidden="true">
          <g id="arrow-up-right">
            <path id="Icon" d="M4.08325 9.91665L9.91659 4.08331M9.91659 4.08331H4.08325M9.91659 4.08331V9.91665" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
          </g>
        </svg>
      </a>
    }
  </div>
</div>