@use '@angular/material/core/tokens/token-utils';

$global-prefix: ngm;
$prefix: (ngm, color);

@include token-utils.use-tokens($prefix, (surface-container-high: null, primary-container: null, on-primary-container: null,)) {
  :host {
    background-color: var(#{token-utils.get-token-variable(surface-container-high)});
  }
  .pac-story-card__button {
    &:hover {
      color: var(#{token-utils.get-token-variable(on-primary-container)});
      background-color: var(#{token-utils.get-token-variable(primary-container)});
    }
    &:focus {
      --tw-ring-color: var(#{token-utils.get-token-variable(primary-container)});
      --tw-ring-offset-color: var(#{token-utils.get-token-variable(surface-container-high)});
    }
  }
}

:host {
  @apply rounded-lg shadow-lg overflow-hidden;
}