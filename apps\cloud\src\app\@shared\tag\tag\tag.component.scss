.content {
  @apply px-2 py-1 rounded-lg flex items-center whitespace-nowrap;
}

:host::ng-deep {
  @apply inline-block w-fit;

  .tag-icon {
    @apply inline-block mr-2;
    svg {
      @apply w-4 h-4;
    }
  }

  &.xs {
    .content {
      @apply px-1 py-0.5 rounded-md text-xs;
    }
    .tag-icon {
      @apply mr-1;
      svg {
        @apply w-3 h-3;
      }
    }
  }
  &.sm {
    .content {
      @apply px-2 py-1 rounded-md text-sm;
    }
    .tag-icon {
      svg {
        @apply w-4 h-4;
      }
    }
  }
  &.lg {
    .content {
      @apply px-4 py-2 rounded-xl text-lg;
    }
    .tag-icon {
      svg {
        @apply w-4 h-4;
      }
    }
  }
}
