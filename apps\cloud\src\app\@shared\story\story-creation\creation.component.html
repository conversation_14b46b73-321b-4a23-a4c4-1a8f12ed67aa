<div class="ngm-theme-dark dark w-[300px] shrink-0 flex flex-col justify-start overflow-auto bg-bluegray-700 text-white p-4 group">
  <form [formGroup]="form" class="flex-1 flex flex-col justify-start items-stretch"
    (ngSubmit)="onApply()">
    <div class="w-full flex justify-start items-center mb-4" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
      <mat-icon displayDensity="cosy" class="-ml-2 opacity-0 group-hover:opacity-80">drag_indicator</mat-icon>
      <span class="text-lg pointer-events-none">
        {{ 'PAC.ACTIONS.CREATE' | translate: { Default: 'Create' } }}
      </span>
      <span class="text-lg pointer-events-none">
        {{ 'PAC.KEY_WORDS.STORY' | translate: { Default: 'Story' } }}
      </span>
    </div>
    <mat-form-field appearance="fill" floatLabel="always" color="accent">
      <mat-label>{{ 'PAC.MENU.STORY.STORY_NAME' | translate: { Default: 'Name' } }}</mat-label>
      <input
        matInput
        placeholder="{{
          'PAC.MENU.STORY.WhatIsTheName' | translate: { Default: 'What is the name of your story' }
        }}?"
        formControlName="name"
        required  
      />
    </mat-form-field>

    <mat-form-field appearance="fill" floatLabel="always" color="accent">
      <mat-label>
        {{ 'PAC.MENU.STORY.Description' | translate: { Default: 'Description' } }}
      </mat-label>
      <textarea
        matInput
        formControlName="description"
        placeholder="{{
          'PAC.MENU.STORY.DescriptionPlaceholder' | translate: { Default: 'Optional, desciption of the story' }
        }}"
      ></textarea>
    </mat-form-field>
  
    @if (collections?.length) {
      <ngm-tree-select
        appearance="fill"
        floatLabel="always"
        formControlName="collectionId"
        color="accent"
        [treeNodes]="collections"
        [label]="'PAC.Project.Collection' | translate: { Default: 'Collection' }"
        searchable
        placeholder="{{
          'PAC.Project.CollectionPlaceholder' | translate: { Default: 'Which collection should this go in' }
        }}?"
        [displayBehaviour]="DisplayBehaviour.descriptionOnly"
      />
    }
  </form>

  <div class="w-full flex justify-end items-center">
    <div ngmButtonGroup>
      <button mat-button mat-dialog-close>
        {{ 'PAC.ACTIONS.CANCEL' | translate: { Default: 'Cancel' } }}
      </button>

      <button mat-raised-button color="accent" [disabled]="form.invalid" (click)="onApply()">
        {{ 'PAC.ACTIONS.CREATE' | translate: { Default: 'Create' } }}
      </button>
    </div>
  </div>
</div>

<div class="w-[400px] flex flex-col">
  <div class="text-lg px-4 py-2 flex justify-between items-center">
      <span>{{ 'PAC.MENU.STORY.Model' | translate: { Default: 'Model' } }}</span>
      <pac-inline-search class="ml-2" displayDensity="compact"
        [formControl]="searchControl"
        (click)="$event.stopPropagation()"
      />
  </div>
  <mat-selection-list
    class="flex-1 overflow-auto ngm-selection-list"
    displayDensity="cosy"
    [formControl]="modelsControl"
    [compareWith]="compareWithModel"
  >
    @for (model of models(); track model.id) {
      <mat-list-option [value]="model">
        <span class="whitespace-nowrap text-ellipsis overflow-hidden">{{ model.name }}</span>
      </mat-list-option>
    }
  </mat-selection-list>
</div>
