{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
		{
			"name": "Attach NestJS",
			"port": 9229,
			"request": "attach",
			"skipFiles": [
				"<node_internals>/**"
			],
			"type": "node"
		},
        {
			"type": "node",
			"request": "attach",
			"name": "Debug Server",
			"sourceMaps": true,
			"restart": true,
			"port": 9229
		},
		{
			"name": "Debug NestJS",
			"type": "node",
			"request": "launch",
			"runtimeExecutable": "nodemon",
			"program": "${workspaceFolder}/apps/api/src/main.ts",
			"restart": true,
			"console": "integratedTerminal",
			"internalConsoleOptions": "neverOpen",
			"env": {
			  "TS_NODE_PROJECT": "apps/api/tsconfig.app.json"
			},
			"skipFiles": ["<node_internals>/**"],
			"sourceMaps": true
		}
    ]
}