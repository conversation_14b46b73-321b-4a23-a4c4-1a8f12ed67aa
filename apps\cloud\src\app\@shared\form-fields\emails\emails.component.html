<mat-form-field class="w-full" [appearance]="appearance">
  <mat-label>{{ label }}</mat-label>

  <mat-chip-grid #chipGrid [attr.aria-label]="label" multiple [formControl]="formControl">
    <mat-chip-row
      *ngFor="let keyword of keywords"
      [value]="keyword"
      [removable]="removable"
      (removed)="removeKeyword(keyword)"
    >{{keyword}}
    <button *ngIf="removable" matChipRemove>
      <mat-icon>cancel</mat-icon>
    </button>
  </mat-chip-row>
  </mat-chip-grid>
    
  <input
    [placeholder]="placeholder"
    [matChipInputFor]="chipGrid"
    [matChipInputAddOnBlur]="addOnBlur"
    (matChipInputTokenEnd)="addKeywordFromInput($event)"
    >
</mat-form-field>
