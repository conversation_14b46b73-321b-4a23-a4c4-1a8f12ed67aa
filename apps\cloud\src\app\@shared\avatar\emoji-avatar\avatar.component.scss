:host {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;

  &.editable {
    @apply ring-1 ring-transparent hover:ring-gray-200 ring-offset-1 ring-offset-white dark:ring-offset-black;
  }
  &.focused {
    @apply ring-gray-300;
  }
}
:host.xs {
  width: 1.8rem;
  height: 1.8rem;
}
:host.small {
  width: 2rem;
  height: 2rem;
}
:host.large {
  width: 3rem;
  height: 3rem;
}
:host::ng-deep {
  ngx-emoji {
    line-height: 1;
  }
}
