<ngm-dialog [title]="title" [form]="formGroup" (apply)="onApply()">
    <div class="flex flex-col">
        <mat-form-field appearance="fill">
            <mat-label>
              {{ 'PAC.KEY_WORDS.Project' | translate: {Default: 'Project'} }}
            </mat-label>
            <mat-select [formControl]="projectControl" [compareWith]="compareWith"
              panelClass="ngm-select-panel ngm-density__cosy"
            >
              <mat-option *ngFor="let project of projects$ | async" [value]="project">
                <div class="flex items-center">
                    <mat-icon *ngIf="project.modelNotInProject" displayDensity="compact" class="shrink-0"
                        matTooltip="Semantic model not in this project"
                    >block</mat-icon> {{project.name}}
                </div>
              </mat-option>
            </mat-select>
        </mat-form-field>
    
        <mat-form-field appearance="fill">
            <mat-label>
              {{ 'PAC.KEY_WORDS.Story' | translate: {Default: 'Story'} }}
            </mat-label>
            <mat-select [formControl]="storyControl" [compareWith]="compareWith"
              panelClass="ngm-select-panel ngm-density__cosy">
              <mat-option *ngFor="let story of stories$ | async" [value]="story">
                <div class="flex items-center ">
                    <mat-icon *ngIf="story.modelNotInStory" displayDensity="compact" class="shrink-0"
                        matTooltip="Semantic model not in this story"
                    >block</mat-icon> {{story.name}}
                </div>
              </mat-option>
            </mat-select>
        </mat-form-field>
    
        <mat-form-field *ngIf="points$ | async as points" appearance="fill" >
            <mat-label>
              {{ 'PAC.KEY_WORDS.StoryPoint' | translate: {Default: 'Story Point'} }}
            </mat-label>
            <mat-select [formControl]="pointControl" [compareWith]="compareWith"
              panelClass="ngm-select-panel ngm-density__cosy">
              <mat-option *ngFor="let point of points" [value]="point">
                {{point.name}}
              </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    
</ngm-dialog>