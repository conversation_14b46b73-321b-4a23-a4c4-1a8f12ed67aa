<mat-accordion>
  @for (feature of features$ | async; track feature.code) {
	<mat-expansion-panel hideToggle class="mat-elevation-z"
		[disabled]="!enabledFeature(feature)">
	  <mat-expansion-panel-header>
		<mat-panel-title>
		  {{ 'PAC.Feature.Features.' + feature.code + '.Name' | translate: {Default: feature.name } }}
		</mat-panel-title>
		<mat-panel-description class="flex justify-end items-center">
		  {{ 'PAC.Feature.Features.' + feature.code + '.Description' | translate: {Default: feature.description } }}
		</mat-panel-description>
		
		<mat-slide-toggle class="inline-flex" [checked]="enabledFeature(feature)" (change)="featureChanged($event.checked, feature)"></mat-slide-toggle>
	  </mat-expansion-panel-header>
	
	@if (feature.children?.length > 0) {
	  <mat-list displayDensity="cosy">
		@for (child of feature.children; track child.code) {
		  <mat-list-item>
			<mat-checkbox [checked]="enabledFeature(child)" (change)="featureChanged($event.checked, child)">
				<div class="flex items-center gap-4">
					<span>{{ 'PAC.Feature.Features.' + child.code + '.Name' | translate: {Default: child.name } }}</span>
					<span class="text-sm italic">{{ 'PAC.Feature.Features.' +  child.code + '.Description' | translate: {Default: child.description } }}</span>
				</div>
			</mat-checkbox>
		  </mat-list-item>
	    }
	  </mat-list>
	}
	</mat-expansion-panel>
  }
</mat-accordion>

@if (loading()) {
  <div class="absolute left-0 top-0 w-full h-full flex justify-center items-center">
	<mat-progress-spinner [mode]="'indeterminate'" [diameter]="22" color="accent" />
  </div>
}