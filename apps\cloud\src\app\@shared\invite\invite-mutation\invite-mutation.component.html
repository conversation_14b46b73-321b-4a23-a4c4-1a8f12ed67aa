<header cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle class="mb-2 px-2">
  <div class="cursor-move text-2xl font-medium text-text-primary">
    {{ 'PAC.MENU.InviteUsers' | translate: {Default: 'Invite Users'} }}
  </div>
</header>

<div class="flex-1 p-2 overflow-auto">
  <pac-email-invite-form #emailInviteForm
	  [invitationType]="invitationType"
  />
</div>

<div class="w-full flex justify-end">
  <div ngmButtonGroup>
    <button mat-stroked-button (click)="cancel()">
      {{ 'COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'} }}
    </button>
    <button mat-raised-button color="accent" cdkFocusInitial
      [disabled]="emailInviteForm.form.invalid"
      (click)="onApply()">
      {{ 'COMPONENTS.COMMON.APPLY' | translate: {Default: 'Apply'} }}
    </button>
  </div>
</div>
