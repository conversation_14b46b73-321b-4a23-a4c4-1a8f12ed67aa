@if (parameter(); as parameter) {
  @switch (parameter.type) {
    @case(eParameterType.FLOAT) {
      <mat-slider class="w-[200px]" ngm-density small [min]="parameter.min" [max]="parameter.max" [step]="0.1" discrete>
        <input matSliderThumb [value]="value$()"
            (dragEnd)="updateValue($event.value)">
      </mat-slider>

      <input matInput class="shrink-0 block ml-4 pl-3 w-24 h-8 appearance-none outline-none rounded-lg bg-gray-100 text-[13px] text-gra-900" [max]="parameter.max" [min]="parameter.min" [step]="0.1" type="number"
        [(ngModel)]="value$"
        [ngModelOptions]="{standalone: true}"
      >
    }
    @case (eParameterType.INT) {
      <mat-slider class="w-[200px]" ngm-density small [min]="parameter.min" [max]="parameter.max" discrete>
        <input matSliderThumb [value]="value$()"
          (dragEnd)="updateValue($event.value)" >
        </mat-slider>

      <input matInput class="shrink-0 block ml-4 pl-3 w-24 h-8 appearance-none outline-none rounded-lg bg-gray-100 text-[13px] text-gra-900" [max]="parameter.max" [min]="parameter.min" [step]="1" type="number"
        [(ngModel)]="value$"
        [ngModelOptions]="{standalone: true}">
    }

    @case (eParameterType.BOOLEAN) {
      <mat-slide-toggle [(ngModel)]="value$"
        [ngModelOptions]="{standalone: true}" ngm-density="xs"/>
    }

    @default {
      @if (parameter.options) {
          <select class="w-36 h-full rounded-lg border-0 bg-gray-100 py-1.5 pl-3 pr-4 sm:text-sm sm:leading-6 focus-visible:outline-none focus-visible:bg-gray-200 group-hover:bg-gray-200 cursor-pointer"
            [(ngModel)]="value$"
            [ngModelOptions]="{standalone: true}">
            @for (option of parameter.options; track option) {
                <option [value]="option" class="px-2 py-1 rounded-lg hover:bg-hover-bg">{{option}}</option>
              }  
          </select>
        } @else {
          <input matInput class="w-full ml-4 flex items-center px-3 h-8 appearance-none outline-none rounded-lg bg-gray-100 text-[13px] text-gra-900"
            [(ngModel)]="value$"
            [ngModelOptions]="{standalone: true}"
          >
      }
    }
  }
}