<div class="flex justify-between items-center">
  <div class="flex items-center cursor-pointer"
    (click)="toggle()">
    @if (type()) {
      <i class="ri-arrow-drop-right-line" [class.rotate-90]="expand()"></i>
    }
    <div class="uppercase font-semibold text-sm">
      {{'PAC.Xpert.ErrorHandling' | translate: {Default: 'Error Handling'} }}
    </div>
  </div>

  <button type="button" class="btn disabled:btn-disabled btn-secondary btn-small"
    [cdkMenuTriggerFor]="typeMenu"
  >
    {{selectedOption()?.label | i18n}}
    <i class="ri-arrow-down-s-line"></i>
  </button>
</div>

@if (expand()) {
@switch (type()) {
  @case('defaultValue') {
    <div class="px-4 pt-2">
      <div class="mb-2 text-sm text-text-tertiary">
        {{'PAC.Xpert.ErrorHandlingDefault' | translate: {Default: 'When an error occurs, specify a static output content.'} }}
        &nbsp;
        <a [href]="helpWebsite() + '/docs/ai/workflow/error-handling'" target="_blank" class="text-text-accent opacity-70 hover:opacity-100">
          {{'PAC.Xpert.LearnMore' | translate: {Default: 'Learn More'} }}
        </a></div>
      <xpert-parameters-form [parameters]="defaultValueSchema()" 
        [ngModel]="defaultValue()"
        (ngModelChange)="updateDefaultValue($event)"/>
    </div>
  }
  @case('failBranch') {
    <div class="mt-2 p-4 rounded-[10px] bg-gray-50">
      <div class="flex justify-start items-center gap-4">
        <div class="flex items-center justify-center mb-2 w-8 h-8 rounded-[10px] border-[0.5px] bg-components-card-bg shadow-sm">
          <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="remixicon w-5 h-5 text-text-tertiary">
            <path d="M18 3C19.6569 3 21 4.34315 21 6C21 7.65685 19.6569 9 18 9H15C13.6941 9 12.5831 8.16562 12.171 7.0009L11 7C9.9 7 9 7.9 9 9L9.0009 9.17102C10.1656 9.58312 11 10.6941 11 12C11 13.3059 10.1656 14.4169 9.0009 14.829L9 15C9 16.1 9.9 17 11 17L12.1707 17.0001C12.5825 15.8349 13.6937 15 15 15H18C19.6569 15 21 16.3431 21 18C21 19.6569 19.6569 21 18 21H15C13.6941 21 12.5831 20.1656 12.171 19.0009L11 19C8.79 19 7 17.21 7 15H5C3.34315 15 2 13.6569 2 12C2 10.3431 3.34315 9 5 9H7C7 6.79086 8.79086 5 11 5L12.1707 5.00009C12.5825 3.83485 13.6937 3 15 3H18ZM18 17H15C14.4477 17 14 17.4477 14 18C14 18.5523 14.4477 19 15 19H18C18.5523 19 19 18.5523 19 18C19 17.4477 18.5523 17 18 17ZM8 11H5C4.44772 11 4 11.4477 4 12C4 12.5523 4.44772 13 5 13H8C8.55228 13 9 12.5523 9 12C9 11.4477 8.55228 11 8 11ZM18 5H15C14.4477 5 14 5.44772 14 6C14 6.55228 14.4477 7 15 7H18C18.5523 7 19 6.55228 19 6C19 5.44772 18.5523 5 18 5Z"></path>
          </svg>
        </div>
  
        <div class="mb-1 text-sm font-medium text-text-primary">
          {{'PAC.Xpert.GoToFailBranch' | translate: {Default: 'Go to the canvas to customize the fail branch logic.'} }}
        </div>
      </div>
      
      <div class="system-xs-regular text-text-tertiary">
        {{'PAC.Xpert.GoToFailBranchDesc' | translate: {Default: 'When the fail branch is activated, exceptions thrown by nodes will not terminate the process. Instead, it will automatically execute the predefined fail branch, allowing you to flexibly provide error messages, reports, fixes, or skip actions.'} }}
        &nbsp;
        <a [href]="helpWebsite() + '/docs/ai/workflow/error-handling'" target="_blank" class="text-text-accent opacity-70 hover:opacity-100">
          {{'PAC.Xpert.LearnMore' | translate: {Default: 'Learn More'} }}
        </a>
      </div>
    </div>
  }
}
}

<ng-template #typeMenu>
  <div cdkMenu class="cdk-menu__large max-w-xs overflow-hidden">
    @for (option of typeOptions; track option.value) {
      <div cdkMenuItem class="flex flex-col items-start text-sm mb-2"
        [class.active]="type() === option.value"
        (click)="setType(option.value)"
      >
        <div class="w-full text-left font-semibold mb-1">{{option.label | i18n}}</div>
        <div class="w-full text-left line-clamp-3 whitespace-normal">{{option.description | i18n}}</div>
      </div>
    }
  </div>
</ng-template>