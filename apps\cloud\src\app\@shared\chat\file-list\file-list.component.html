<div class="flex justify-between items-center p-1 border-b border-solid border-divider-deep">
  <div>

  </div>

  <div>
    <button type="button" class="btn btn-secondary btn-sm" (click)="emptyFiles()">
      {{'PAC.Chat.Empty' | translate: {Default: 'Empty'} }}
    </button>
  </div>
</div>
<div class="flex-1 min-h-0 overflow-auto mt-4">
  <div class="flex flex-col first:pt-0 pt-2">
    @for (item of flatFiles(); track item) {
      <div class="group flex justify-between items-center gap-2 flex-1 min-w-0 min-h-10 px-2 rounded-lg hover:bg-hover-bg">
        @for (level of item.levels; track $index) {
          <div class="shrink-0 w-[14px] h-full flex justify-center">
            <div class="w-[1px] min-h-[36px] h-full bg-neutral-200"></div>
          </div>
        }

        @if (item.hasChildren) {
          @if (item.expanded) {
            <i class="ri-arrow-down-s-line cursor-pointer" (click)="toggleFolder(item)"></i>
          } @else {
            <i class="ri-arrow-right-s-line cursor-pointer" (click)="toggleFolder(item)"></i>
          }
        } @else {
          <i class="ri-folder-line opacity-0"></i>
        }
        <pac-file-icon [fileType]="item.filePath | fileType" [directory]="item.hasChildren"/>
        <div class="flex flex-col justify-center flex-1 min-w-0 max-w-[100%] min-h-[36px]">
          <span class="text-base text-text-primary text-ellipsis overflow-hidden whitespace-nowrap">{{item.filePath}}</span>
          <span class="text-xs font-mono text-text-tertiary">{{item.createdAt | relative}}</span>
        </div>
        <div
          class="flex items-center justify-center cursor-pointer rounded-md w-8 h-8 opacity-0 group-hover:opacity-100"
          type="button"
          #trigger="cdkMenuTriggerFor"
          [class.opacity-100]="trigger.isOpen()"
          [cdkMenuTriggerFor]="menu"
          [cdkMenuTriggerData]="{file: item}"
        >
          <i class="ri-more-line"></i>
        </div>
      </div>
    }
  </div>
</div>

@if (loading())  {
  <ngm-spin class="absolute top-0 left-0 w-full h-full" />
}

<ng-template #menu let-file="file">
  <div cdkMenu class="cdk-menu__medium">
    <!-- <div cdkMenuItem (click)="preview(file)">
      {{'PAC.Chat.Preview' | translate: {Default: 'Preview'} }}
    </div> -->
    <div cdkMenuItem (click)="download(file.url)">
      <i class="ri-file-download-line mr-1"></i>{{'PAC.Chat.Download' | translate: {Default: 'Download'} }}
    </div>
    @if (editable()) {
      <div cdkMenuItem class="danger" (click)="deleteFile(file)">
        <i class="ri-delete-bin-4-line mr-1"></i>{{'PAC.Chat.Delete' | translate: {Default: 'Delete'} }}
      </div>
    }
  </div>
</ng-template>