@if (fields(); as fields) {
  <formly-form [form]="form"
    [options]="formOptions"
    [fields]="fields"
    [model]="value$()"
    (modelChange)="updateValues($event)"
  />
} @else {
  @for (param of parameters(); track param.name) {
    <parameter [schema]="param" [required]="isRequired(param.name)"
      [ngModel]="value$()?.[param.name]"
      (ngModelChange)="updateValue(param.name, $event)" />
  }
}