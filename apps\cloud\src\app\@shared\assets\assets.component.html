@if (searching$ | async) {
  <mat-progress-bar
    mode="query"
    color="accent"
    class="top-0 left-0 w-full"
    style="position: absolute; height: 2px;"
  />
}

@if (!search) {
  <div class="my-2 mx-4 text-base">
    {{ 'PAC.SHARED.Assets.RecentlyViewed' | translate: { Default: 'Recently viewed' } }}
  </div>
}

@if (isEmpty$ | async) {
  <div class="pac-home__assets-empty p-8 flex flex-col justify-start items-center">
    <span class="shopping-cart text-lg font-notoColorEmoji">🛒</span>
    <span>
      {{
        'PAC.SHARED.Assets.NoRelevantDigitalAssets' | translate: { Default: 'No relevant digital assets were found' }
      }}!
    </span>
  </div>
}

<mat-list class="assets-list" displayDensity="cosy">
  <ng-container *ngFor="let asset of searchAssets$ | async">
    <mat-list-item *ngIf="asset.type === 'story'" class="cursor-pointer group" [routerLink]="['/story', asset.id]">
      <span matListItemIcon class="text-lg font-notoColorEmoji">📖</span>
      <div matListItemTitle class="flex flex-col">
        <a class="text-base text-ellipsis overflow-hidden" [routerLink]="['/story', asset.id]">{{ asset.name }}</a>
        <div class="flex-1 flex justify-between items-baseline text-xs  text-opacity-60 group-hover:text-opacity-80
          text-slate-500 dark:text-slate-300">
          <span class="flex-1 basis-1/2">
            {{ 'PAC.KEY_WORDS.Story' | translate: { Default: 'Story' } }}
          </span>
          <div class="flex-1 basis-0"></div>
        </div>
        <div *ngIf="asset.description" class="description text-sm border-l-2 text-opacity-60 group-hover:text-opacity-80
          border-l-bluegray-100 text-slate-500 dark:text-slate-300 dark:border-l-bluegray-300">
          {{ asset.description }}
        </div>
      </div>
    </mat-list-item>

    <mat-list-item *ngIf="asset.type === 'indicator'" class="cursor-pointer group" [routerLink]="['/indicator/viewer', asset.id]">
      <span matListItemIcon class="text-lg">💹</span>
      <div matListItemTitle class="flex flex-col">
        <a class="text-base" [routerLink]="['/indicator/viewer', asset.id]">{{ asset.name }}</a>
        <div class="flex-1 flex justify-between items-baseline text-xs text-opacity-60 group-hover:text-opacity-80
          text-slate-500 dark:text-slate-300" >
          <div class="mr-2">
            {{ 'PAC.KEY_WORDS.Indicator' | translate: { Default: 'Indicator' } }}
          </div>
          <div class="flex-1 text-ellipsis overflow-hidden text-right">{{ asset.code }}</div>
        </div>
        <div *ngIf="asset.business" class="description text-xs border-l-2
          border-l-bluegray-100 dark:text-slate-300 dark:border-l-bluegray-300">
          {{ asset.business }}
        </div>
      </div>
    </mat-list-item>

    <mat-list-item *ngIf="asset.type === 'semanticModel'" class="cursor-pointer group" [routerLink]="['/models', asset.id]">
      <span matListItemIcon class="text-lg font-notoColorEmoji">🧊</span>
      <div matListItemTitle class="flex flex-col">
        <a class="text-base" [routerLink]="['/models', asset.id]">{{ asset.name }}</a>

        <div class="flex-1 flex justify-between items-baseline text-xs text-opacity-60 group-hover:text-opacity-80
          text-slate-500 dark:text-slate-300">
          <span class="flex-1 basis-1/2">
            {{ 'PAC.KEY_WORDS.SemanticModel' | translate: { Default: 'Semantic Model' } }}
          </span>
          <div class="flex-1 basis-0"></div>
        </div>

        <div *ngIf="asset.description" class="description text-xs border-l-2
          border-l-bluegray-100 dark:text-slate-300 dark:border-l-bluegray-300">
          {{ asset.description }}
        </div>
      </div>
    </mat-list-item>
  </ng-container>
</mat-list>
