<h2 cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle class="p-4">
  <div class="flex-1 text-lg truncate">{{tool().name | i18n}}</div>
</h2>

<div class="flex flex-col items-stretch px-4 py-2">
  <mcp-toolset-tool-test [tool]="tool()" visibleAll />
</div>

<div class="w-full flex justify-end sticky bottom-0 p-4 bg-components-card-bg">
  <button mat-button cdkFocusInitial (click)="cancel()">{{'PAC.ACTIONS.Close' | translate: {Default: 'Close'} }}</button>
</div>