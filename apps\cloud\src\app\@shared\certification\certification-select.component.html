<header mat-dialog-title cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
    <div style="pointer-events: none;">
        {{ 'PAC.MENU.AddCertifications' | translate: {Default: 'Add Certifications'} }}
    </div>
</header>

<mat-dialog-content class="mat-typography flex flex-col">
    <ngm-select appearance="fill" color="accent" displayBehaviour="descriptionOnly"
        [label]=" 'PAC.KEY_WORDS.Certification' | translate: { Default: 'Certification' } "
        [selectOptions]="certifications$ | async"
        searchable
        [(ngModel)]="certificationId"
    >
    </ngm-select>
</mat-dialog-content>

<mat-dialog-actions align="end">
    <div ngmButtonGroup>
        <button mat-flat-button mat-dialog-close>
            {{ 'COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'} }}
        </button>
        <button mat-raised-button color="accent" cdkFocusInitial [matDialogClose]="certificationId">
            {{ 'COMPONENTS.COMMON.APPLY' | translate: {Default: 'Apply'} }}
        </button>
    </div>
</mat-dialog-actions>
