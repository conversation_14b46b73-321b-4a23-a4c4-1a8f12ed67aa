<header cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle class="p-4 cursor-move">
  <h4 class="text-xl font-medium">
    {{ 'PAC.USERS_PAGE.ADD_USER' | translate: {Default: 'Add User'} }}
  </h4>
</header>

<div class="flex flex-col w-full px-8 py-4">
  <pac-user-basic-info-form [password]="true"
    [isAdmin]="isAdmin"
    [isSuperAdmin]="isSuperAdmin"
    #userBasicInfo
  />
</div>

<div class="w-full flex justify-end p-2">
  <div ngmButtonGroup>
    <button mat-button cdkFocusInitial (click)="cancel()">
      {{ 'COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'} }}
    </button>
    <button mat-raised-button color="accent"
      [disabled]="userBasicInfo.invalid"
      (click)="add()">
      {{ 'COMPONENTS.COMMON.APPLY' | translate: {Default: 'Apply'} }}
    </button>
  </div>
</div>
