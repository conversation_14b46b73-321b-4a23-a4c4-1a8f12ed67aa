<div class="shrink-0 flex items-center justify-between mb-3 p-4 pb-0 ">
  <div class="text-text-primary system-xl-semibold">
    {{'PAC.Xpert.Variable' | translate: {Default: 'Variable'} }}
  </div>

  <div class="flex items-center justify-center w-6 h-6 cursor-pointer text-text-secondary"
    (click)="cancel.emit()"
  >
    <i class="ri-close-line"></i>
  </div>
</div>

<form [formGroup]="form" class="px-4 py-2 max-h-[480px] overflow-y-auto">
  <div class="mb-4">
    <div class="mb-1 h-6 flex items-center text-text-secondary system-sm-semibold">
      {{'PAC.Xpert.Name' | translate: {Default: 'Name'} }}
    </div>
    <div class="flex">
      <input formControlName="name" matInput tabindex="0" class="block px-3 w-full h-8 bg-components-input-bg-normal system-sm-regular rounded-lg border border-transparent appearance-none outline-none caret-primary-600 hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-xs placeholder:system-sm-regular placeholder:text-components-input-text-placeholder"
        [placeholder]="'PAC.Xpert.VariableName' | translate: {Default: 'Variable name'}" type="text">
    </div>
  </div>
  <div class="mb-4 flex gap-2">
    <div class="grow flex flex-col">
      <div class="mb-1 h-6 flex items-center text-text-secondary system-sm-semibold">
        {{'PAC.Xpert.Type' | translate: {Default: 'Type'} }}
      </div>

      <ngm-select formControlName="type"
        [placeholder]="'PAC.Xpert.InputType' | translate: {Default: 'Input type'}"
        [selectOptions]="VariableTypeOptions"
      />
    </div>
  </div>
  <div class="mb-4">
    <div class="mb-1 h-6 flex items-center justify-between text-text-secondary system-sm-semibold">
      <div>{{'PAC.Xpert.DefaultValue' | translate: {Default: 'Default Value'} }}</div>
    </div>
    <div class="flex">
      <input formControlName="value" matInput tabindex="0" class="block px-3 w-full h-8 bg-components-input-bg-normal system-sm-regular rounded-lg border border-transparent appearance-none outline-none caret-primary-600 hover:border-components-input-border-hover hover:bg-components-input-bg-hover focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-xs placeholder:system-sm-regular placeholder:text-components-input-text-placeholder"
        [type]="type === 'secret' ? 'password' : 'text'"
        [placeholder]="'PAC.Xpert.DefaultValueEmpty' | translate: {Default: 'Default value, can be empty'}">
    </div>
  </div>
</form>

<div class="p-4 pt-2 flex flex-row-reverse rounded-b-2xl">
    <div class="flex gap-2">
      <button type="button" class="btn disabled:btn-disabled btn-secondary btn-medium"
        (click)="cancel.emit()"
      >{{'PAC.Xpert.Cancel' | translate: {Default: 'Cancel'} }}</button>
      <button type="button" class="btn disabled:btn-disabled btn-primary btn-medium"
        (click)="saved.emit(form.value)">{{'PAC.Xpert.Save' | translate: {Default: 'Save'} }}</button>
    </div>
</div>