<form [formGroup]="formGroup" class="">
  <div class="mb-2">
    <label class="font-medium my-2 inline-block">
      {{ 'PAC.Xpert.Name' | translate: { Default: 'Name' } }}
    </label>
    <div class="relative w-full">
      <input matInput
        class="w-full py-[7px] bg-components-input-bg-normal border border-transparent 
          text-components-input-text-filled hover:bg-components-input-bg-hover hover:border-components-input-border-hover
          focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-sm
          placeholder:text-components-input-text-placeholder
          appearance-none outline-none caret-primary-600 px-3 rounded-lg system-sm-regular grow h-10"
        [placeholder]="'PAC.Xpert.TaskName' | translate: {Default: 'Name'}"
        formControlName="name"
      />
    </div>
  </div>

  <div class="mb-2">
    <label class="font-medium my-2 inline-block">
      {{ 'PAC.Chat.Agent' | translate: { Default: 'Agent' } }}
    </label>
    <div class="relative w-full">
      <input matInput
        class="w-full py-[7px] bg-components-input-bg-normal border border-transparent 
          text-components-input-text-filled hover:bg-components-input-bg-hover hover:border-components-input-border-hover
          focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-sm
          placeholder:text-components-input-text-placeholder
          appearance-none outline-none caret-primary-600 px-3 rounded-lg system-sm-regular grow h-10"
        [placeholder]="'PAC.Chat.Agent' | translate: {Default: 'Agent'}"
        formControlName="agentKey"
      />
    </div>
  </div>

  <div class="mb-2">
    <label class="font-medium my-2 inline-block">
      {{ 'PAC.Xpert.Instructions' | translate: { Default: 'Instructions' } }}
    </label>
    <div class="relative w-full">
      <textarea matInput
        class="w-full py-[7px] bg-components-input-bg-normal border border-transparent 
          text-components-input-text-filled hover:bg-components-input-bg-hover hover:border-components-input-border-hover
          focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-sm
          placeholder:text-components-input-text-placeholder
          appearance-none outline-none caret-primary-600 px-3 rounded-lg system-sm-regular grow h-10"
        [placeholder]="'PAC.Xpert.Instructions' | translate: {Default: 'Instructions'}"
        formControlName="prompt"
        cdkTextareaAutosize
        cdkAutosizeMinRows="2"
        cdkAutosizeMaxRows="5"
      ></textarea>
    </div>
  </div>

  <div class="mb-2">
    <label class="font-medium my-2 inline-block">
      {{ 'PAC.Xpert.Schedule' | translate: { Default: 'Schedule' } }}
    </label>
    <div class="relative w-full">
      <input matInput
        class="w-full py-[7px] bg-components-input-bg-normal border border-transparent 
          text-components-input-text-filled hover:bg-components-input-bg-hover hover:border-components-input-border-hover
          focus:bg-components-input-bg-active focus:border-components-input-border-active focus:shadow-sm
          placeholder:text-components-input-text-placeholder
          appearance-none outline-none caret-primary-600 px-3 rounded-lg system-sm-regular grow h-10"
        [placeholder]="'PAC.Xpert.Schedule' | translate: {Default: 'Schedule'}"
        formControlName="schedule"
      />
    </div>
  </div>
</form>