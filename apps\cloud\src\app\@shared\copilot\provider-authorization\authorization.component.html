<div class="flex justify-between items-center mb-2 cursor-move" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
  <div class="text-xl font-semibold text-gray-900">{{'PAC.Copilot.Setup' | translate: {Default: 'Setup'} }} {{provider().label | i18n}}</div>
  <img
    alt="provider-icon"
    [src]="icon()"
    class="w-auto h-8"
  />
</div>

<div class="relative">
  <copilot-credential-form #credentialsForm [credentialFormSchemas]="credential_form_schemas()" [(ngModel)]="credentials" />
  @if (loading()) {
    <ngm-spin class="absolute top-0 left-0 w-full h-full" />
  }
</div>

<div class="mt-1 mb-4 border-t-[0.5px] border-t-gray-100"></div>

@if (error()) {
  <div class="p-2 text-sm text-text-destructive">{{error()}}</div>
}

<div class="sticky bottom-0 flex justify-between items-center mt-2 -mx-2 pt-4 px-2 pb-6 flex-wrap gap-y-2 bg-white">
  @if (help()) {
    <a
      [href]="help().url | i18n"
      target="_blank"
      rel="noopener noreferrer"
      class="inline-flex items-center text-xs text-primary-600"
      >
      {{ help().title | i18n }}
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        class="ml-1 w-3 h-3"
      >
        <g id="link-external-02">
          <path
            id="Icon"
            d="M10.5 4.5L10.5 1.5M10.5 1.5H7.49999M10.5 1.5L6 6M5 1.5H3.9C3.05992 1.5 2.63988 1.5 2.31901 1.66349C2.03677 1.8073 1.8073 2.03677 1.66349 2.31901C1.5 2.63988 1.5 3.05992 1.5 3.9V8.1C1.5 8.94008 1.5 9.36012 1.66349 9.68099C1.8073 9.96323 2.03677 10.1927 2.31901 10.3365C2.63988 10.5 3.05992 10.5 3.9 10.5H8.1C8.94008 10.5 9.36012 10.5 9.68099 10.3365C9.96323 10.1927 10.1927 9.96323 10.3365 9.68099C10.5 9.36012 10.5 8.94008 10.5 8.1V7"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
          ></path>
        </g>
      </svg>
    </a>
  }

  <div class="flex items-center gap-1">
    <button
      type="button"
      class="btn disabled:btn-disabled btn-secondary btn-large mr-2"
      (click)="cancel()"
    >{{'PAC.ACTIONS.Cancel' | translate: {Default: 'Cancel'} }}</button>
    <button type="button" class="btn disabled:btn-disabled btn-primary btn-large"
      [disabled]="credentialsForm.invalid || loading() || !dirty()"
      (click)="apply()"
    >{{'PAC.ACTIONS.Save' | translate: {Default: 'Save'} }}</button>
  </div>
</div>
