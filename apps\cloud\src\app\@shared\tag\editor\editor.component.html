<mat-form-field class="flex-1" appearance="fill" [color]="color">
  <mat-label>
    {{ 'PAC.KEY_WORDS.Tags' | translate: {Default: 'Tags'} }}
  </mat-label>
  <mat-chip-grid #chipGrid aria-label="Tag selection">
    <mat-chip-row *ngFor="let tag of tags()" [ngClass]="'bg-'+tag.color+'-100'" class="bg-blue-100" (removed)="remove(tag)">
      {{tag.name}}
      <button matChipRemove>
        <mat-icon>cancel</mat-icon>
      </button>
    </mat-chip-row>
  </mat-chip-grid>

  <input placeholder="{{'PAC.ACTIONS.NewTag' | translate: {Default: 'New tag'} }}..." #tagInput
          [formControl]="tagCtrl"
          [matAutocomplete]="auto"
          [matChipInputFor]="chipGrid"
          [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
          [matChipInputAddOnBlur]="addOnBlur"
          (matChipInputTokenEnd)="add($event)">

  <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
    <mat-option *ngFor="let tag of filterdTags()" [value]="tag">
      <div class="flex flex-col justify-center items-start">
        <span [ngmHighlight]="highlight" [caseSensitive]="false" customClasses="bg-transparent font-semibold text-amber-500"
          [content]="tag.name" class="text-base">{{tag.name}}</span>
        <span *ngIf="tag.description" class="text-sm text-slate-300"
          [content]="tag.description" [ngmHighlight]="highlight" [caseSensitive]="false" customClasses="bg-transparent font-semibold text-amber-500">{{tag.description}}</span>
      </div>
    </mat-option>
  </mat-autocomplete>
</mat-form-field>