<div class="menu-trigger group flex justify-start items-center text-sm px-2 p-1 rounded-lg bg-gray-100 hover:bg-gray-200"
  #trigger="cdkMenuTriggerFor"
  [cdkMenuTriggerFor]="varsMenu"
>
  <i class="ri-chat-thread-line mr-1 text-primary-400 group-hover:text-primary-500 "></i>
  @if (variable()) {
    @if (group()?.group) {
      <div class="max-w-[60px] truncate font-medium"
        [title]="group().group.name"
      >{{group().group.description | i18n}}</div>
      <span class="mx-1">/</span>
    }
    <div class="grow flex items-center truncate ml-0.5 text-text-accent font-medium">
      <!-- <div class="mr-1">
        @switch (variableType()) {
          @case (eXpertParameterTypeEnum.STRING) {
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" data-icon="LetterSpacing01" aria-hidden="true">
              <g id="letter-spacing-01">
                <path id="Icon" d="M9 13L15 13M7 17L11.2717 7.60225C11.5031 7.09323 11.6188 6.83872 11.7791 6.75976C11.9184 6.69115 12.0816 6.69115 12.2209 6.75976C12.3812 6.83872 12.4969 7.09323 12.7283 7.60225L17 17M21 3V21M3 3L3 21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </g>
            </svg>
          }
          @case ('array[string]') {
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" data-icon="AlignLeft" aria-hidden="true">
              <g id="align-left">
                <path id="Icon" d="M16 10H3M20 6H3M20 14H3M16 18H3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </g>
            </svg>
          }
          @case (eXpertParameterTypeEnum.BOOLEAN) {
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" data-icon="CheckDone01" aria-hidden="true">
              <g id="check-done-01">
                <path id="Icon" d="M6 15L8 17L12.5 12.5M8 8V5.2C8 4.0799 8 3.51984 8.21799 3.09202C8.40973 2.71569 8.71569 2.40973 9.09202 2.21799C9.51984 2 10.0799 2 11.2 2H18.8C19.9201 2 20.4802 2 20.908 2.21799C21.2843 2.40973 21.5903 2.71569 21.782 3.09202C22 3.51984 22 4.0799 22 5.2V12.8C22 13.9201 22 14.4802 21.782 14.908C21.5903 15.2843 21.2843 15.5903 20.908 15.782C20.4802 16 19.9201 16 18.8 16H16M5.2 22H12.8C13.9201 22 14.4802 22 14.908 21.782C15.2843 21.5903 15.5903 21.2843 15.782 20.908C16 20.4802 16 19.9201 16 18.8V11.2C16 10.0799 16 9.51984 15.782 9.09202C15.5903 8.71569 15.2843 8.40973 14.908 8.21799C14.4802 8 13.9201 8 12.8 8H5.2C4.0799 8 3.51984 8 3.09202 8.21799C2.71569 8.40973 2.40973 8.71569 2.21799 9.09202C2 9.51984 2 10.0799 2 11.2V18.8C2 19.9201 2 20.4802 2.21799 20.908C2.40973 21.2843 2.71569 21.5903 3.09202 21.782C3.51984 22 4.07989 22 5.2 22Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
              </g>
            </svg>
          }
          @case (eXpertParameterTypeEnum.NUMBER) {
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 text-gray-500" data-icon="Hash02" aria-hidden="true">
              <g id="hash-02">
                <path id="Icon" d="M4.74999 1.5L3.24999 10.5M8.74998 1.5L7.24998 10.5M10.25 4H1.75M9.75 8H1.25" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round"></path>
              </g>
            </svg>
          }
        }
      </div> -->

      <span class="text-sm mr-1">{{ variable().name }}</span>
      <span class="flex-1 text-xs italic truncate text-text-secondary">{{variable().description | i18n}}</span>
      <span class="ml-2 px-1 text-sm rounded-md bg-gray-50 text-primary-300">{{variable().type}}</span>
    </div>
  } @else {
    <div class="text-text-secondary">{{'PAC.Xpert.StateVariable' | translate: {Default: 'State variable'} }}</div>
  }
</div>


<ng-template #varsMenu>
  <xpert-variable-panel class="border-[0.5px] border-solid border-slate-200 rounded-xl shadow-lg bg-components-card-bg" (click)="$event.stopPropagation()" 
    [variables]="variables()"
    [ngModel]="value$()"
    (ngModelChange)="setVariable($event);trigger.close()"
    (close)="trigger.close()"
  />
</ng-template>
