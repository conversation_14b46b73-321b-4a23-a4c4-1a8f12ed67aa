<div class="ngm-theme-dark dark w-[300px] shrink-0 flex flex-col justify-start overflow-auto bg-bluegray-700 text-white p-4 group">
  <div class="w-full flex justify-start items-center mb-4" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
    <mat-icon displayDensity="cosy" class="-ml-2 opacity-0 group-hover:opacity-80">drag_indicator</mat-icon>
    <span class="text-lg pointer-events-none">
      {{ 'Story.Common.StoryTemplates' | translate: { Default: 'Story Templates' } }}
    </span>
  </div>

  <ngm-search class="my-2" [formControl]="searchControl"></ngm-search>

  <mat-nav-list class="ngm-nav-list flex-1">
     <mat-list-item class="mb-2" [class.active]="activedLink === c_suggested" (click)="activeLink(c_suggested)">
      <div class="flex items-center">
        <mat-icon >star</mat-icon>
        <span > {{ 'Story.Common.Suggested' | translate: {Default: 'Suggested'} }} </span>
      </div>
     </mat-list-item>

     <mat-list-item [class.active]="activedLink === c_my"
      (click)="activeLink(c_my)">
      <div class="flex items-center">
        <mat-icon></mat-icon>
        <span > {{ 'Story.Common.My' | translate: {Default: 'My'} }} </span>
      </div>
      </mat-list-item>
  </mat-nav-list>

</div>


<div class="flex-1 flex flex-col">

  <div class="p-4 flex flex-col gap-4">
    <div class="flex items-center gap-2">
      <label class="w-10 shrink-0">
        {{ 'Story.Template.Type' | translate: {Default: 'Type'} }}
      </label>
      <ngm-tags class="flex-1" selectable [tags]="[
        {
          value: StoryTemplateType.Template,
          label: 'Story.Template.Template' | translate: {Default: 'Template'}
        },
        {
          value: StoryTemplateType.Theme,
          label: 'Story.Template.Theme' | translate: {Default: 'Theme'}
        }]"
        [(ngModel)]="templateTypes"
      ></ngm-tags>
    </div>
  
    <div class="flex items-center gap-2">
      <label class="w-10 shrink-0">{{ 'Story.Template.Tags' | translate: {Default: 'Tags'} }}</label>
      <pac-tags class="flex-1 " [tags]="tags" [selectable]="true"
        (selectedChange)="onTagsChange($event)"
      ></pac-tags>
    </div>
  </div>

  <div class="p-4 pt-0 flex-1 overflow-auto grid grid-cols-3 gap-4" [@listAnimation]="(templates$ | async)?.length">

    <div *ngFor="let template of templates$ | async; trackBy: trackById" class="pac-template h-64 overflow-hidden flex flex-col" [class.active]="template.id === appliedTemplateId">
      <div class="pac-template__preview flex-1 m-1 rounded-lg border-2 overflow-hidden relative group
      border-slate-200 bg-slate-50 dark:border-slate-800 dark:bg-slate-700">
        <img [src]="template.preview?.url ?? template.thumbnail" class="w-full h-44 object-cover" loading="lazy"/>

        <pac-tags [tags]="template.tags" [selectable]="true" (selectedChange)="onTagsChange($event)" class="absolute bottom-0 left-0 w-full p-2 z-10"></pac-tags>

        <div class="w-full h-full absolute top-0 left-0 flex flex-col justify-center items-center gap-2 opacity-0 bg-slate-500/20 group-hover:opacity-100 group-hover:backdrop-blur-lg rounded-md overflow-hidden">
          <button mat-raised-button color="primary" class="w-20" (click)="useTemplate(template.id)">
            {{ 'Story.Template.Use' | translate: {Default: "Use"} }}
          </button>
          <!-- <button mat-raised-button class="w-20">Preview</button> -->

          <button *ngIf="activedLink === 'my'" mat-icon-button class="pac-template__remove absolute right-2 top-2 text-white" displayDensity="cosy" ngmAppearance="danger"
            (click)="deleteTemplate(template)">
            <mat-icon>close</mat-icon>
          </button>
        </div>

      </div>
      
      <div class="flex flex-col justify-start items-start h-20">
        <div class="text-base overflow-hidden text-ellipsis" [ngmHighlight]="highlight" [content]="template.name" customClasses="bg-transparent font-semibold text-amber-500">{{ template.name }}</div>
        <div class="text-sm overflow-hidden text-ellipsis">
          {{ template.createdBy | user}}
        </div>
      </div>
    </div>

  </div>
</div>