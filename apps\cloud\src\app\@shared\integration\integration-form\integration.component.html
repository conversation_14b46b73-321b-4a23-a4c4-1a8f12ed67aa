<form [formGroup]="formGroup" class="w-full grow p-8 overflow-y-auto flex flex-col gap-2">
  <emoji-avatar formControlName="avatar" large editable class="self-center rounded-xl overflow-hidden shadow-lg"/>
      
  <ngm-input class="flex-1" [label]="'PAC.KEY_WORDS.Name' | translate: {Default: 'Name'}"
    formControlName="name"
  />

  <div class="flex-1 min-w-full flex flex-col">
    <label class="ngm-input-label shrink-0">{{'PAC.KEY_WORDS.Description' | translate: {Default: 'Description'} }}</label>
    <textarea class="ngm-input-element" matInput formControlName="description"
      cdkTextareaAutosize
      cdkAutosizeMinRows="1"
      cdkAutosizeMaxRows="5">
    </textarea>
  </div>

  @if (schema(); as fields) {
    <formly-form [form]="optionsForm"
      [options]="formOptions"
      [fields]="fields"
      [model]="optionsModel"
    />
  } @else if (integrationProvider()) {
    <list-content-loader />
  }

  @if(webhookUrl()) {
    <div>
      <p>{{ 'PAC.Integration.WebhookUrl' | translate: {Default: 'Webhook url'} }}:</p>
      <pre class="px-2 py-1 rounded-md text-sm whitespace-break-spaces bg-gray-50 dark:bg-white/10">{{webhookUrl()}}</pre>
    </div>
  }

  <div class="w-full flex justify-start">
    <button type="button" class="btn disabled:btn-disabled btn-secondary btn-medium"
      (click)="test()">{{ 'PAC.KEY_WORDS.Test' | translate: {Default: 'Test'} }}</button>
  </div>
</form>

@if (loading()) {
  <ngm-spin class="absolute left-0 top-0 w-full h-full" />
}