<div class="absolute right-0 top-0 flex pl-[13px] ">
  <svg width="13" height="20" viewBox="0 0 13 20" fill="none"
    xmlns="http://www.w3.org/2000/svg" class="text-gray-50"
  >
    <path id="Shape" d="M0 0H13V20C9.98017 20 7.26458 18.1615 6.14305 15.3576L0 0Z"
      fill="currentColor"></path>
  </svg>
  <div class="text-xs font-semibold uppercase h-6 rounded-tr-xl bg-gray-50 pr-2 leading-6 text-text-tertiary"
  >{{knowledgebase().type || eKnowledgebaseTypeEnum.Standard}}</div>
</div>

<div class="flex">
  <emoji-avatar [avatar]="knowledgebase().avatar" class="shrink-0 rounded-lg overflow-hidden shadow-sm" />
  <div class="ml-3 w-0 grow">
    <div class="flex h-5 items-center">
      <div class="font-semibold truncate text-zinc-700" [ngmHighlight]="searchText()" [content]="knowledgebase().name">
        {{knowledgebase().name}}
      </div>
    </div>
    <div class="flex h-4 items-center space-x-0.5 mt-0.5">
      <span class="system-xs-regular shrink-0 text-text-tertiary">{{knowledgebase().createdBy | user}}</span>
      <span class="system-xs-regular shrink-0 text-text-quaternary">/</span>
      <span class="system-xs-regular w-0 shrink-0 grow truncate text-text-tertiary">{{knowledgebase().permission}}</span>
    </div>
  </div>
</div>

<div class="system-xs-regular text-text-tertiary h-10 line-clamp-2 mt-3">
  {{knowledgebase().description}}
</div>

<div class="flex h-5 items-center">
  <div class="flex items-center space-x-1 text-text-tertiary">
    <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24"
      height="24" fill="currentColor" class="h-3 w-3 shrink-0">
      <path
          d="M9 2V4H5L4.999 14H18.999L19 4H15V2H20C20.5523 2 21 2.44772 21 3V21C21 21.5523 20.5523 22 20 22H4C3.44772 22 3 21.5523 3 21V3C3 2.44772 3.44772 2 4 2H9ZM18.999 16H4.999L5 20H19L18.999 16ZM17 17V19H15V17H17ZM13 2V7H16L12 11L8 7H11V2H13Z"></path>
    </svg>
    <div class="system-xs-regular">{{knowledgebase().documentNum | number}}</div>
  </div>
  <div class="system-xs-regular mx-2 text-text-quaternary">·</div>
  <div class="flex flex-wrap space-x-2 overflow-hidden">
  </div>
</div>

<div class="absolute right-3 bottom-3 flex items-center gap-2">
  <ng-content select="[action]"></ng-content>
</div>