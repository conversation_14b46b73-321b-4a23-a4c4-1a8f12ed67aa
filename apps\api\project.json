{"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/api", "main": "apps/api/src/main.ts", "tsConfig": "apps/api/tsconfig.app.json", "assets": ["apps/api/src/assets"], "isolatedConfig": true, "webpackConfig": "apps/api/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "api:build"}, "configurations": {"development": {"buildTarget": "api:build:development"}, "production": {"buildTarget": "api:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/api/jest.config.ts"}}}, "tags": []}