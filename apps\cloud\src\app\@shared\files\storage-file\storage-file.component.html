<pac-file-icon [fileType]="file().name | fileType" small class="opacity-80 group-hover/chip:opacity-100"/>
<span class="truncate max-w-full me-auto sm:max-w-full flex-1">
  {{file().name}}
</span>

<button type="button" class="btn inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium leading-[normal] cursor-pointer focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:opacity-60 disabled:cursor-default transition-colors duration-100 disabled:hover:text-secondary disabled:hover:bg-inherit h-6 w-6 rounded-full ml-1 p-0.5 flex-shrink-0
        danger opacity-0 group-hover/chip:opacity-100"
  (click)="delete()"
>
  <i class="ri-close-line"></i>
</button>

<mat-progress-bar class="absolute bottom-0 left-0 w-full" mode="determinate" [value]="progress()"
  [color]="progress() === 100 ? 'primary' : 'accent'" />