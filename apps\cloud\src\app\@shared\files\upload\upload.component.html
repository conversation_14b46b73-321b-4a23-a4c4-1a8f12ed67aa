<div class="container relative flex-1 flex border-2 border-dashed rounded-lg"
  ngmDnd (fileDropped)="onFileDropped($event)"
  >
  <input type="file" #fileDropRef id="fileDropRef" multiple class="invisible"
    (change)="fileBrowseHandler($event.target)"
    (click)="fileDropRef.value=null;"/>
  <div class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center">
    <svg width="100" height="100">
      <circle cx="50" cy="50" r="40" fill="#80808050"/>
      <rect x="25" y="48" width="50" height="4" fill="white"/>
      <rect x="48" y="25" width="4" height="50" fill="white"/>
    </svg>

    <p class="mb-2 text-sm text-gray-500 dark:text-gray-400">
      {{ 'PAC.MODEL.CREATE_TABLE.DragandDropFile' | translate: {Default: 'Drag and drop to add file'} }}
    </p>
    <h3 class="text-sm text-gray-500 dark:text-gray-400">
      {{ 'PAC.MODEL.CREATE_TABLE.Or' | translate: {Default: 'or'} }}
    </h3>
    
    <label for="fileDropRef" class="block text-sm cursor-pointer
      py-2 px-4
      rounded-full border-0 font-semibold
      text-slate-500
      bg-gray-100
      hover:bg-bluegray-200
      dark:bg-gray-800 dark:hover:bg-gray-700 dark:text-neutral-200"
      >
      <span class="sr-only">Choose profile photo</span>
      <span>
        {{ 'PAC.MODEL.CREATE_TABLE.BrowseForFile' | translate: {Default: 'Browse for file'} }}
      </span>
    </label>

    @if (description()) {
      <div class="mx-32 text-xs text-text-tertiary">{{description()}}</div>
    }

    <div class="w-full flex flex-col stretch">
      @for (item of files(); track i; let i = $index) {
        <div class="flex justify-between items-center px-2 hover:bg-black/5 dark:hover:bg-white/10">
          {{ item.file.name }}
          <button mat-icon-button ngmAppearance="danger" displayDensity="cosy" (click)="removeFile(item)">
            <mat-icon>close</mat-icon> 
          </button>
        </div>
        <mat-progress-bar mode="determinate" [value]="item.progress" />
      }
    </div>
  </div>
</div>
