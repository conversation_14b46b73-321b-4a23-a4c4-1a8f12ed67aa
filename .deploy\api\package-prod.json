{"name": "xpert.ai", "version": "3.0", "license": "AGPL-3.0", "scripts": {"start": "nx serve", "build": "nx build", "test": "nx test", "build:all": "yarn rimraf dist && yarn nx build contracts && yarn nx build common && yarn nx build config && yarn nx build auth && yarn nx build server && yarn nx build adapter && yarn nx build analytics && yarn nx build api"}, "private": true, "dependencies": {"@langchain/community": "0.3.27", "@langchain/core": "0.3.40", "@langchain/langgraph": "0.2.53", "@langchain/ollama": "0.1.5", "@langchain/openai": "0.4.2", "@nestjs/common": "^8.0.0", "@nestjs/core": "^8.0.0", "@nestjs/platform-express": "^8.0.0", "@swc/helpers": "~0.5.0", "idb-keyval": "^6.0.2", "langchain": "0.3.15", "money-clip": "^3.0.5", "reflect-metadata": "^0.1.13", "rxjs": "^7.0.0", "tslib": "^2.3.0"}, "devDependencies": {}, "workspaces": ["packages/*"], "resolutions": {"@langchain/core": "0.3.40", "@modelcontextprotocol/sdk": "1.9.0"}}