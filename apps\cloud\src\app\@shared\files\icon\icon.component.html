@if (directory()) {
  <img class="inline" src="data:image/png;base64,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">
} @else {
  @switch (fileType()) {
    @case ('code') {
      <svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#4999E9"></path><path opacity="0.8" d="M20.6685 6.66647C20.6685 8.38469 22.0613 9.77759 23.7796 9.77759H28.4462L20.6685 1.99981V6.66647Z" fill="#7CBDFF"></path><g opacity="0.9"><path d="M12.2146 23.0075C10.8351 21.6055 9.41533 20.2051 8.00342 18.8399C9.32659 17.5371 10.7038 16.1826 12.0868 14.8106C12.4508 15.1731 12.8166 15.5372 13.1831 15.9025C12.1737 16.9083 11.1695 17.9061 10.19 18.8828C11.2178 19.8927 12.2378 20.9105 13.2498 21.9361C12.9037 22.2922 12.5586 22.6494 12.2146 23.0075Z" fill="white"></path><path d="M20.1101 22.9923C19.7678 22.6361 19.4246 22.2809 19.0803 21.9267C20.0955 20.9008 21.1189 19.883 22.1503 18.8735C21.173 17.9015 20.17 16.9067 19.1604 15.9025C19.5269 15.5378 19.8925 15.1745 20.2562 14.8131C21.6404 16.1831 23.0167 17.5325 24.3368 18.8272C22.9219 20.1886 21.4969 21.5883 20.1101 22.9923Z" fill="white"></path><path d="M15.8827 24.1754H14.4272L16.5372 13.7883H18.0544L15.8827 24.1754Z" fill="white"></path><path d="M12.2146 23.0075C10.8351 21.6055 9.41533 20.2051 8.00342 18.8399C9.32659 17.5371 10.7038 16.1826 12.0868 14.8106C12.4508 15.1731 12.8166 15.5372 13.1831 15.9025C12.1737 16.9083 11.1695 17.9061 10.19 18.8828C11.2178 19.8927 12.2378 20.9105 13.2498 21.9361C12.9037 22.2922 12.5586 22.6494 12.2146 23.0075Z" stroke="white" stroke-width="0.233333"></path><path d="M20.1101 22.9923C19.7678 22.6361 19.4246 22.2809 19.0803 21.9267C20.0955 20.9008 21.1189 19.883 22.1503 18.8735C21.173 17.9015 20.17 16.9067 19.1604 15.9025C19.5269 15.5378 19.8925 15.1745 20.2562 14.8131C21.6404 16.1831 23.0167 17.5325 24.3368 18.8272C22.9219 20.1886 21.4969 21.5883 20.1101 22.9923Z" stroke="white" stroke-width="0.233333"></path><path d="M15.8827 24.1754H14.4272L16.5372 13.7883H18.0544L15.8827 24.1754Z" stroke="white" stroke-width="0.233333"></path></g></svg>
    }
    @case ('zip') {
      <svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#F8A100"></path><path opacity="0.8" d="M20.6665 6.66672C20.6665 8.38494 22.0594 9.77783 23.7776 9.77783H28.4443L20.6665 2.00005V6.66672Z" fill="#FFCE76"></path><path d="M16.0952 14.9524V12.7935H14V10.635H16.0952V8.47619H14V6.31733H16.0952V4.15886H14V2H16.0952V4.15886H18.1905V6.31733H16.0952V8.47619H18.1905V10.635H16.0952V12.7935H18.1905V21.0476H14V14.9524H16.0952ZM17.4286 17.2381H14.7619V20.2857H17.4286V17.2381Z" fill="white"></path></svg>
    }
    @default {
      <figure class="grid aspect-square place-items-center bg-no-repeat bg-center shrink-0" 
        style="background-image: url(&quot;/assets/icons/file-light.svg&quot;);">
        @switch (fileType()) {
          @case ('xlsx') {
            <svg stroke="currentColor" fill="currentColor" stroke-width="0" viewBox="0 0 256 256" 
              class="size-3 text-text-tertiary hover:text-text-tertiary" role="img" aria-label="Microsoft Excel" height="1em" width="1em" xmlns="http://www.w3.org/2000/svg">
              <path d="M200,24H72A16,16,0,0,0,56,40V64H40A16,16,0,0,0,24,80v96a16,16,0,0,0,16,16H56v24a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V40A16,16,0,0,0,200,24Zm-40,80h40v48H160Zm40-16H160V80a16,16,0,0,0-16-16V40h56ZM72,40h56V64H72ZM40,80H144v79.83c0,.06,0,.11,0,.17s0,.11,0,.17V176H40ZM72,192h56v24H72Zm72,24V192a16,16,0,0,0,16-16v-8h40v48ZM65.85,146.88,81.59,128,65.85,109.12a8,8,0,0,1,12.3-10.24L92,115.5l13.85-16.62a8,8,0,1,1,12.3,10.24L102.41,128l15.74,18.88a8,8,0,0,1-12.3,10.24L92,140.5,78.15,157.12a8,8,0,0,1-12.3-10.24Z"></path>
            </svg>
          }
          @default {
            <svg viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#4D81E8"></path><path d="M20.6685 6.66647C20.6685 8.38469 22.0613 9.77759 23.7796 9.77759H28.4462L20.6685 1.99981V6.66647Z" fill="#9CC3F4"></path><path opacity="0.9" d="M10.1685 18.2363H21.8351" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M10.1685 14.3472H12.1129" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M15.0293 14.3472H16.9737" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M10.1685 21.8333H21.8351" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path>
            </svg>
          }
        }
      </figure>
    }
  }
}