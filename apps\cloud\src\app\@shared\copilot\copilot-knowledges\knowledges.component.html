<div class="flex items-center justify-between flex-wrap">
  <div class="w-[200px] flex gap-2 items-center">
    
  </div>

  <div class="flex gap-2 justify-end items-center !h-8">
    
    <input #fileUpload type="file" class="file-input invisible"
      (change)="handleUploadChange($event)"
      (click)="fileUpload.value=null;">
    <ng-content></ng-content>
    <button type="button" class="btn disabled:btn-disabled btn-medium shrink-0 h-10"
      (click)="downloadTemplate()">
      <i class="ri-download-2-line"></i>
      {{ 'PAC.Copilot.Examples.DownloadTemplate' | translate: {Default: 'Download template'} }}
    </button>
    <button type="button" class="btn disabled:btn-disabled btn-medium shrink-0 h-10"
      (click)="fileUpload.click()">
      <i class="ri-upload-2-line"></i>
      {{ 'PAC.Copilot.UploadFile' | translate: {Default: 'Upload File'} }}
    </button>
    <button type="button" class="btn disabled:btn-disabled btn-primary btn-medium shrink-0 h-10"
      (click)="addExample()">
      <i class="ri-sticky-note-add-line"></i>
      {{ 'PAC.Copilot.NewExample' | translate: {Default: 'New Example'} }}
    </button>
  </div>
</div>

<div class="filter-bar shrink-0 p-2 flex items-center gap-4 overflow-hidden max-w-full">
  <ngm-select class="filter-bar__item" [label]="'PAC.Copilot.Examples.Command' | translate: {Default: 'Command'} "
    valueKey="key"
    searchable
    [displayBehaviour]="eDisplayBehaviour.descriptionOnly"
    [selectOptions]="commands()"
    [(ngModel)]="commandFilter"
    [disabled]="loading()"
  />

  <span class="flex-1"></span>
</div>

<ngm-table class="flex-1 overflow-hidden" [columns]="columns()" [data]="items()" displayDensity="cosy" paging />

@if (loading()) {
  <ngm-spin class="absolute left-0 top-0 w-full h-full flex justify-center items-center"></ngm-spin>
}

<ng-template #vector let-vector>
  @if (vector) {
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5 text-text-secondary">
      <path d="M20.4668 8.69379L20.7134 8.12811C21.1529 7.11947 21.9445 6.31641 22.9323 5.87708L23.6919 5.53922C24.1027 5.35653 24.1027 4.75881 23.6919 4.57612L22.9748 4.25714C21.9616 3.80651 21.1558 2.97373 20.7238 1.93083L20.4706 1.31953C20.2942 0.893489 19.7058 0.893489 19.5293 1.31953L19.2761 1.93083C18.8442 2.97373 18.0384 3.80651 17.0252 4.25714L16.308 4.57612C15.8973 4.75881 15.8973 5.35653 16.308 5.53922L17.0677 5.87708C18.0555 6.31641 18.8471 7.11947 19.2866 8.12811L19.5331 8.69379C19.7136 9.10792 20.2864 9.10792 20.4668 8.69379ZM2 4C2 3.44772 2.44772 3 3 3H14V5H4V19H20V11H22V20C22 20.5523 21.5523 21 21 21H3C2.44772 21 2 20.5523 2 20V4ZM7 8H17V11H15V10H13V14H14.5V16H9.5V14H11V10H9V11H7V8Z"></path>
    </svg>
  }
</ng-template>

<ng-template #actionTemplate let-id="id" let-input="input">
  <div class="flex items-center">
    <button class="group px-2 py-1 rounded-md bg-transparent hover:bg-hover-bg" type="button"
      [cdkMenuTriggerFor]="actionMenu"
      [cdkMenuTriggerData]="{id, input}"
    >
      <div class="">
        <i class="ri-more-line"></i>
      </div>
    </button>
  </div>
</ng-template>

<ng-template #actionMenu let-id="id" let-input="input">
  <div cdkMenu class="cdk-menu__medium">
    <div cdkMenuItem (click)="editExample(id)" >
      <i class="ri-edit-line mr-1"></i>
      {{ 'PAC.ACTIONS.Edit' | translate: {Default: 'Edit'} }}</div>
    <div class="w-full border-b border-solid border-divider-regular"></div>
    <div cdkMenuItem class="danger" (click)="deleteExample(id, input)">
      <i class="ri-delete-bin-line mr-1"></i>
      {{ 'PAC.ACTIONS.Delete' | translate: {Default: 'Delete'} }}</div>
  </div>
</ng-template>