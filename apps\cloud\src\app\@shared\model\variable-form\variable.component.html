<header cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle class="pb-2 cursor-move">
  <h4 class="text-xl font-semibold uppercase pointer-events-none">
    {{ 'PAC.MODEL.EditVariable' | translate: {Default: 'Edit Variable'} }}
  </h4>
</header>

<div class="flex flex-col gap-2 p-4">
  <div class="flex justify-between item-center gap-4">
    <ngm-input class="" [label]="'PAC.KEY_WORDS.Name' | translate: {Default: 'Name'}" disabled [ngModel]="name()"/>
    <ngm-input [label]="'PAC.KEY_WORDS.Caption' | translate: {Default: 'Caption'}" [(ngModel)]="caption"/>
  </div>

  <div>
    <div class="mb-1">{{'PAC.KEY_WORDS.SemanticType' | translate: {Default: 'Semantic Type'} }}</div>
    <ngm-select nullable [selectOptions]="SemanticOptions" [(ngModel)]="semantic" />
  </div>

  @if (!semantic()) {
    <div class="mt-2">
      <div class="mb-1">{{'PAC.KEY_WORDS.DefaultValue' | translate: {Default: 'Default Value'} }}</div>
      <ngm-variable [label]="variable().caption" displayDensity="cosy"
        [dataSettings]="dataSettings()"
        [variable]="variable()"
        [(ngModel)]="defaultLow"
      />
    </div>
  }

  <div class="mt-2">
    <ngm-checkbox [label]="'PAC.KEY_WORDS.Required' | translate: {Default: 'Required'} " 
      [(ngModel)]="required" />
  </div>

  <div class="mt-2">
    <ngm-checkbox [label]="'PAC.KEY_WORDS.Visible' | translate: {Default: 'Visible'} " 
      [(ngModel)]="visible" />
  </div>
</div>

<div class="w-full flex justify-end items-center">
  <div ngmButtonGroup>
    <button mat-button (click)="cancel()">
        {{ 'COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'} }}
    </button>
    <button mat-raised-button color="accent" cdkFocusInitial (click)="apply()">
        {{ 'COMPONENTS.COMMON.Confirm' | translate: {Default: 'Confirm'} }}
    </button>
  </div>
</div>