@if (step(); as step) {
  @switch (step.type) {
    @case (eChatMessageStepCategory.File) {
      <i class="ri-file-list-3-line"></i>
    }
    @case (eChatMessageStepCategory.Files) {
      <i class="ri-file-list-3-line"></i>
    }
    @case (eChatMessageStepCategory.Program) {
      <i class="ri-terminal-box-line"></i>
    }
    @default {
      @if (step.toolset) {
        @switch (step.toolset) {
          @case ('project') {
            <i class="ri-building-line"></i>
          }
          @case ('transfer_to') {
            <i class="ri-exchange-2-fill"></i>
          }
          @case ('knowledge') {
            <i class="ri-book-shelf-fill"></i>
          }
          @case ('knowledgebase') {
            <i class="ri-book-shelf-fill"></i>
          }
          @case ('project-tasks') {
            <i class="ri-task-fill"></i>
          }
          @case ('memories') {
            <i class="ri-brain-line"></i>
          }
          @case ('mcp') {
            <emoji-avatar [avatar]="avatar()" xs class="shrink-0 overflow-hidden rounded-lg scale-75"/>
          }
          @default {
            <img src="/api/xpert-toolset/builtin-provider/{{step.toolset}}/icon" alt="{{step.toolset}}" class="rounded-md">
          }
        }
      }
    }
  }
}