<div class="p-2 pb-0 w-full">
  <ul class="p-1 flex items-center justify-center gap-2 bg-hover-bg rounded-xl"
    cdkListbox
    [cdkListboxValue]="[type()]"
    (cdkListboxValueChange)="type.set($event.value[0])">
    <li class="p-2 flex-1 flex justify-center items-center h-8 rounded-xl text-sm shrink-0 font-medium cursor-pointer"
      [ngClass]="type()==='emoji' ? 'shadow-md bg-components-card-bg' : ''"
      [cdkOption]="'emoji'">
      <span class="text-lg">🤖</span> &nbsp; {{ 'PAC.KEY_WORDS.Emoticons' | translate: {Default: 'Emoticons'} }}</li>
    <li class="p-2 flex-1 flex justify-center items-center h-8 rounded-xl text-sm shrink-0 font-medium cursor-pointer"
      [ngClass]="type()==='image' ? 'shadow-md bg-components-card-bg' : ''"
      [cdkOption]="'image'">
      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" >
        <g id="image-plus">
          <path id="Icon" d="M8.33333 2.00016H5.2C4.0799 2.00016 3.51984 2.00016 3.09202 2.21815C2.71569 2.4099 2.40973 2.71586 2.21799 3.09218C2 3.52001 2 4.08006 2 5.20016V10.8002C2 11.9203 2 12.4803 2.21799 12.9081C2.40973 13.2845 2.71569 13.5904 3.09202 13.7822C3.51984 14.0002 4.07989 14.0002 5.2 14.0002H11.3333C11.9533 14.0002 12.2633 14.0002 12.5176 13.932C13.2078 13.7471 13.7469 13.208 13.9319 12.5178C14 12.2635 14 11.9535 14 11.3335M12.6667 5.3335V1.3335M10.6667 3.3335H14.6667M7 5.66683C7 6.40321 6.40305 7.00016 5.66667 7.00016C4.93029 7.00016 4.33333 6.40321 4.33333 5.66683C4.33333 4.93045 4.93029 4.3335 5.66667 4.3335C6.40305 4.3335 7 4.93045 7 5.66683ZM9.99336 7.94559L4.3541 13.0722C4.03691 13.3605 3.87831 13.5047 3.86429 13.6296C3.85213 13.7379 3.89364 13.8453 3.97546 13.9172C4.06985 14.0002 4.28419 14.0002 4.71286 14.0002H10.9707C11.9301 14.0002 12.4098 14.0002 12.7866 13.839C13.2596 13.6366 13.6365 13.2598 13.8388 12.7868C14 12.41 14 11.9303 14 10.9708C14 10.648 14 10.4866 13.9647 10.3363C13.9204 10.1474 13.8353 9.9704 13.7155 9.81776C13.6202 9.6963 13.4941 9.59546 13.242 9.3938L11.3772 7.90194C11.1249 7.7001 10.9988 7.59919 10.8599 7.56357C10.7374 7.53218 10.6086 7.53624 10.4884 7.57529C10.352 7.61959 10.2324 7.72826 9.99336 7.94559Z" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
        </g>
      </svg> &nbsp; {{ 'PAC.KEY_WORDS.Picture' | translate: {Default: 'Picture'} }}</li>
    </ul>
</div>


<div id="default-tab-content">
    <div class="hidden p-4 rounded-lg bg-gray-50 dark:bg-gray-800" id="emoji" role="tabpanel"
      [ngClass]="{active: type() === 'emoji'}">
      <ul class="flex gap-2 p-2" cdkListbox [cdkListboxValue]="[set()]" (cdkListboxValueChange)="set.set($event.value[0])" cdkListboxOrientation="horizontal" >
        @for (item of sets; track item) {
          <li class="px-2 py-1 hover:bg-hover-bg rounded-lg cursor-pointer" [cdkOption]="item"
            [ngClass]="{'bg-hover-bg': item === set()}">{{item || 'native' }}</li>
        }
      </ul>
        <emoji-mart [virtualize]="true" [set]="set()" [isNative]="!set()" (emojiClick)="addEmoji($event)"></emoji-mart>

        <div class="p-3 pb-0">
          <p class="font-medium uppercase text-xs mb-2">{{ 'PAC.KEY_WORDS.ChooseStyle' | translate: {Default: 'Choose Style'} }}  </p>
          <div class="w-full h-full grid grid-cols-8 gap-1">
            @for (color of colors; track color) {
              <div class="emoji-style-preview cursor-pointer hover:ring-1 ring-offset-1 inline-flex w-10 h-10 rounded-lg items-center justify-center ring-1 ring-transparent
                  ring-offset-white dark:ring-offset-black"
                [ngClass]="{active: background() === color}"
                (click)="background.set(color)">
                <div class="w-10 h-10 p-1 flex items-center justify-center rounded-lg" [ngStyle]="{background: color}">
                  @if (emoji()) {
                    <ngx-emoji class="flex" [emoji]="emoji().id" [set]="emoji().set" [isNative]="!emoji().set" [size]="16" />
                  }
                </div>
              </div>
            }
          </div>
        </div>
    </div>
    <div class="hidden px-3 py-1.5 w-[400px]" id="image" role="tabpanel"
      [ngClass]="{active: type() === 'image'}"
    >
      <div class="relative aspect-square bg-gray-50 border-[1.5px] border-gray-200 border-dashed rounded-lg flex flex-col justify-center items-center text-gray-500"
        (dragover)="$event.preventDefault()"
        (dragenter)="$event.preventDefault()"
        (drop)="$event.preventDefault(); handleFileDrop($event)">
        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-[30px] h-[30px] mb-3 pointer-events-none" data-icon="ImagePlus" aria-hidden="true">
          <g id="image-plus">
            <path id="Icon" d="M8.33333 2.00016H5.2C4.0799 2.00016 3.51984 2.00016 3.09202 2.21815C2.71569 2.4099 2.40973 2.71586 2.21799 3.09218C2 3.52001 2 4.08006 2 5.20016V10.8002C2 11.9203 2 12.4803 2.21799 12.9081C2.40973 13.2845 2.71569 13.5904 3.09202 13.7822C3.51984 14.0002 4.07989 14.0002 5.2 14.0002H11.3333C11.9533 14.0002 12.2633 14.0002 12.5176 13.932C13.2078 13.7471 13.7469 13.208 13.9319 12.5178C14 12.2635 14 11.9535 14 11.3335M12.6667 5.3335V1.3335M10.6667 3.3335H14.6667M7 5.66683C7 6.40321 6.40305 7.00016 5.66667 7.00016C4.93029 7.00016 4.33333 6.40321 4.33333 5.66683C4.33333 4.93045 4.93029 4.3335 5.66667 4.3335C6.40305 4.3335 7 4.93045 7 5.66683ZM9.99336 7.94559L4.3541 13.0722C4.03691 13.3605 3.87831 13.5047 3.86429 13.6296C3.85213 13.7379 3.89364 13.8453 3.97546 13.9172C4.06985 14.0002 4.28419 14.0002 4.71286 14.0002H10.9707C11.9301 14.0002 12.4098 14.0002 12.7866 13.839C13.2596 13.6366 13.6365 13.2598 13.8388 12.7868C14 12.41 14 11.9303 14 10.9708C14 10.648 14 10.4866 13.9647 10.3363C13.9204 10.1474 13.8353 9.9704 13.7155 9.81776C13.6202 9.6963 13.4941 9.59546 13.242 9.3938L11.3772 7.90194C11.1249 7.7001 10.9988 7.59919 10.8599 7.56357C10.7374 7.53218 10.6086 7.53624 10.4884 7.57529C10.352 7.61959 10.2324 7.72826 9.99336 7.94559Z" stroke="currentColor" stroke-width="1.25" stroke-linecap="round" stroke-linejoin="round"></path>
          </g>
        </svg>
        <div class="text-base font-medium mb-[2px]">
          <span class="pointer-events-none">{{ 'PAC.KEY_WORDS.DropImageHereOr' | translate: {Default: 'Drop your image here, or'} }}&nbsp;</span>
          <button class="text-components-button-primary-bg" (click)="fileUpload.click()">{{ 'PAC.KEY_WORDS.Browse' | translate: {Default: 'browse'} }}</button>
          <input #fileUpload class="hidden" accept=".png,.jpg,.jpeg,.webp,.gif" type="file" (change)="uploadAvatar($event)"
            (click)="fileUpload.value=null;">
        </div>
        <div class="text-xs pointer-events-none">{{ 'PAC.KEY_WORDS.Supports' | translate: {Default: 'Supports'} }} PNG, JPG, JPEG, WEBP {{ 'PAC.KEY_WORDS.And' | translate: {Default: 'and'} }} GIF</div>

        @if (imageUrl()) {
          <div class="w-16 h-16 rounded-md overflow-hidden shadow-sm">
            <img [src]="imageUrl()">
          </div>
        }
      </div>
    </div>

</div>

<div class="flex justify-end p-4">
  <div ngmButtonGroup>
    <button mat-flat-button mat-dialog-close>
      {{ 'PAC.ACTIONS.CANCEL' | translate: { Default: 'Cancel' } }}
    </button>
    <button mat-raised-button color="accent" cdkFocusInitial (click)="apply()">
      <span>
        {{ 'Story.Common.Save' | translate: { Default: 'Save' } }}
      </span>
    </button>
  </div>
</div>
