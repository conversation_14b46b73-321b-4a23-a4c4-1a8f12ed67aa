<!-- <div class="edit-container pt-2 -ml-4 rounded-t-xl text-sm overflow-hidden text-gray-700"
  [style.height.px]="height"
  > -->
  <ngx-monaco-editor class="!h-full w-full max-h-full"
    [options]="editorOptions()"
    [ngModel]="instruction()"
    (ngModelChange)="instruction.set($event);elementRef.nativeElement.blur()"
    (resized)="onResized()"
    (onInit)="onInit($event)"
  />
<!-- </div> -->

<div class="absolute bottom-0 left-0 w-full flex justify-center h-2 cursor-row-resize"
  (mousedown)="onMouseDown($event)">
  <div class="w-5 h-[3px] rounded-sm bg-gray-300"></div>
</div>