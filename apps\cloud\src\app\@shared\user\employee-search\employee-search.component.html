<header mat-dialog-title cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
    <div style="pointer-events: none;">
        {{ 'PAC.MENU.AddUsers' | translate: {Default: 'Add Users'} }}
    </div>
</header>

<mat-dialog-content class="mat-typography flex flex-col">
    <div class="flex flex-col">
        <mat-form-field appearance="fill" class="self-stretch" >
            <mat-label>{{ 'PAC.KEY_WORDS.Users' | translate: {Default: 'Users'} }}</mat-label>
            <mat-chip-grid #chipGrid>
                <mat-chip-row *ngFor="let user of users"
                    (removed)="remove(user)">
                    <img matChipAvatar *ngIf="user.imageUrl" src="{{user.user.imageUrl}}" alt="Avatar"/>
                    {{user.user | user}}
                    <button matChipRemove>
                        <mat-icon>cancel</mat-icon>
                    </button>
                </mat-chip-row>
            </mat-chip-grid>

            <input
                [placeholder]=" 'PAC.SHARED.User.NameEmail' | translate: {Default: 'Name,Email'} "
                #userInput
                [formControl]="searchControl"
                [matAutocomplete]="auto"
                [matChipInputFor]="chipGrid"
                [matChipInputSeparatorKeyCodes]="separatorKeysCodes"
            >
            <mat-autocomplete #auto="matAutocomplete" (optionSelected)="selected($event)">
                <mat-option *ngFor="let option of users$ | async" [value]="option">
                    <ngm-display-behaviour class="flex-1" [option]="{
                        value: option.user.email,
                        label: userLabel(option.user)
                    }" [highlight]="searchControl.value ?? ''"></ngm-display-behaviour>
                </mat-option>
            </mat-autocomplete>
            <mat-spinner matSuffix *ngIf="loading" diameter="20"></mat-spinner>
        </mat-form-field>

        <mat-radio-group *ngIf="data?.roles" [(ngModel)]="role" displayDensity="cosy">
            <mat-radio-button *ngFor="let role of data.roles" [value]="role.value">{{role.label}}</mat-radio-button>
        </mat-radio-group>
    </div>
</mat-dialog-content>

<mat-dialog-actions align="end">
    <div ngmButtonGroup>
        <button mat-flat-button mat-dialog-close>
            {{ 'COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'} }}
        </button>
        <button mat-raised-button color="accent" cdkFocusInitial (click)="onApply()">
            {{ 'COMPONENTS.COMMON.APPLY' | translate: {Default: 'Apply'} }}
        </button>
    </div>
</mat-dialog-actions>
