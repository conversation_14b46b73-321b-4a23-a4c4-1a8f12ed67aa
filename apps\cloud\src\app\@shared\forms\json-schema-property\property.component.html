<div class="flex justify-between items-center py-1">
  <div>
    @if(required()) {
      <span class="text-text-destructive">*</span>
    }
    {{meta().title || name()}}
  </div>

  @if (type() === 'array') {
    <button class="btn btn-small justify-center w-6 h-6" (click)="addArray()">
      <i class="ri-add-line"></i>
    </button>
  }
</div>

@switch (type()) {
  @case ('string') {
    <input
      class="w-full px-3 text-sm leading-9 text-gray-900 border-0 rounded-lg grow h-9 bg-gray-50 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-gray-200"
      [placeholder]="(meta().description || meta().title || '' ) + (required() ? '' : '(' + ('PAC.KEY_WORDS.Optional' | translate: {Default: 'Optional'}) + ')' )"
      type="text"
      [disabled]="readonly()"
      [ngModel]="value$()"
      (ngModelChange)="update($event)"
    />
  }
  @case ('number') {
    <input
      class="w-full px-3 text-sm leading-9 text-gray-900 border-0 rounded-lg grow h-9 bg-gray-50 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-gray-200"
      [placeholder]="(meta().description || meta().title || '' ) + (required() ? '' : '(' + ('PAC.KEY_WORDS.Optional' | translate: {Default: 'Optional'}) + ')' )"
      type="number"
      [disabled]="readonly()"
      [ngModel]="value$()"
      (ngModelChange)="update($event)"
    />
  }
  @case ('integer') {
    <input
      class="w-full px-3 text-sm leading-9 text-gray-900 border-0 rounded-lg grow h-9 bg-gray-50 focus:outline-none focus:ring-1 focus:ring-inset focus:ring-gray-200"
      [placeholder]="(meta().description || meta().title || '' ) + (required() ? '' : '(' + ('PAC.KEY_WORDS.Optional' | translate: {Default: 'Optional'}) + ')' )"
      type="number"
      [disabled]="readonly()"
      [ngModel]="value$()"
      (ngModelChange)="update($event)"
    />
  }
  @case ('array') {
    <div class="flex flex-col gap-1">
      @for (item of value$(); track item; let i = $index) {
        <json-schema-property [schema]="arraySchema().items" [ngModel]="item" (ngModelChange)="updateArray(i, $event)" />
      }
    </div>
  }
  @case ('boolean') {
    <ngm-slide-toggle [disabled]="readonly()"
      [ngModel]="value$()"
      (ngModelChange)="update($event)" 
    />
  }
  @default {
    <div>Unsupported type: {{type()}}</div>
  }
}