<div class="p-0">
  <h2 class="text-[var(--text-primary)] text-[18px] leading-[24px] font-semibold flex items-center"></h2>
  <p id="radix-:r6s:"></p>
</div>
<header class="flex items-center pt-6 pr-6 pl-6 pb-2.5">
  <h1 class="flex-1 text-[var(--text-primary)] text-lg font-semibold">
      {{ 'PAC.Chat.AllFilesInTask' | translate: {Default: 'All files in this task'} }}
  </h1>
  <div class="flex items-center gap-4">
    <div
      class="flex h-7 w-7 items-center justify-center cursor-pointer hover:bg-hover-bg rounded-md"
      (click)="close()"
    >
      <i class="ri-close-line"></i>
    </div>
  </div>
</header>
<div class="flex-1 min-h-0 flex flex-col">
  <div class="flex-1 min-h-0 overflow-auto px-3 mt-4 pb-4">
    <div class="flex flex-col gap-1 first:pt-0 pt-2">
      @for (item of files(); track item) {
        <div class="group flex justify-between items-center gap-2 flex-1 min-w-0 p-2 rounded-lg hover:bg-hover-bg">
          @switch (item.filePath | fileType) {
            @case ('code') {
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#4999E9"></path><path opacity="0.8" d="M20.6685 6.66647C20.6685 8.38469 22.0613 9.77759 23.7796 9.77759H28.4462L20.6685 1.99981V6.66647Z" fill="#7CBDFF"></path><g opacity="0.9"><path d="M12.2146 23.0075C10.8351 21.6055 9.41533 20.2051 8.00342 18.8399C9.32659 17.5371 10.7038 16.1826 12.0868 14.8106C12.4508 15.1731 12.8166 15.5372 13.1831 15.9025C12.1737 16.9083 11.1695 17.9061 10.19 18.8828C11.2178 19.8927 12.2378 20.9105 13.2498 21.9361C12.9037 22.2922 12.5586 22.6494 12.2146 23.0075Z" fill="white"></path><path d="M20.1101 22.9923C19.7678 22.6361 19.4246 22.2809 19.0803 21.9267C20.0955 20.9008 21.1189 19.883 22.1503 18.8735C21.173 17.9015 20.17 16.9067 19.1604 15.9025C19.5269 15.5378 19.8925 15.1745 20.2562 14.8131C21.6404 16.1831 23.0167 17.5325 24.3368 18.8272C22.9219 20.1886 21.4969 21.5883 20.1101 22.9923Z" fill="white"></path><path d="M15.8827 24.1754H14.4272L16.5372 13.7883H18.0544L15.8827 24.1754Z" fill="white"></path><path d="M12.2146 23.0075C10.8351 21.6055 9.41533 20.2051 8.00342 18.8399C9.32659 17.5371 10.7038 16.1826 12.0868 14.8106C12.4508 15.1731 12.8166 15.5372 13.1831 15.9025C12.1737 16.9083 11.1695 17.9061 10.19 18.8828C11.2178 19.8927 12.2378 20.9105 13.2498 21.9361C12.9037 22.2922 12.5586 22.6494 12.2146 23.0075Z" stroke="white" stroke-width="0.233333"></path><path d="M20.1101 22.9923C19.7678 22.6361 19.4246 22.2809 19.0803 21.9267C20.0955 20.9008 21.1189 19.883 22.1503 18.8735C21.173 17.9015 20.17 16.9067 19.1604 15.9025C19.5269 15.5378 19.8925 15.1745 20.2562 14.8131C21.6404 16.1831 23.0167 17.5325 24.3368 18.8272C22.9219 20.1886 21.4969 21.5883 20.1101 22.9923Z" stroke="white" stroke-width="0.233333"></path><path d="M15.8827 24.1754H14.4272L16.5372 13.7883H18.0544L15.8827 24.1754Z" stroke="white" stroke-width="0.233333"></path></g></svg>
            }
            @case ('zip') {
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#F8A100"></path><path opacity="0.8" d="M20.6665 6.66672C20.6665 8.38494 22.0594 9.77783 23.7776 9.77783H28.4443L20.6665 2.00005V6.66672Z" fill="#FFCE76"></path><path d="M16.0952 14.9524V12.7935H14V10.635H16.0952V8.47619H14V6.31733H16.0952V4.15886H14V2H16.0952V4.15886H18.1905V6.31733H16.0952V8.47619H18.1905V10.635H16.0952V12.7935H18.1905V21.0476H14V14.9524H16.0952ZM17.4286 17.2381H14.7619V20.2857H17.4286V17.2381Z" fill="white"></path></svg>
            }
            @default {
              <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M3.55566 26.8889C3.55566 28.6071 4.94856 30 6.66678 30H25.3334C27.0517 30 28.4446 28.6071 28.4446 26.8889V9.77778L20.6668 2H6.66678C4.94856 2 3.55566 3.39289 3.55566 5.11111V26.8889Z" fill="#4D81E8"></path><path d="M20.6685 6.66647C20.6685 8.38469 22.0613 9.77759 23.7796 9.77759H28.4462L20.6685 1.99981V6.66647Z" fill="#9CC3F4"></path><path opacity="0.9" d="M10.1685 18.2363H21.8351" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M10.1685 14.3472H12.1129" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M15.0293 14.3472H16.9737" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path><path opacity="0.9" d="M10.1685 21.8333H21.8351" stroke="white" stroke-width="1.75" stroke-linecap="square" stroke-linejoin="round"></path></svg>
            }
          }
          <div class="flex flex-col flex-1 min-w-0 max-w-[100%]">
            <span class="text-base text-[var(--text-primary)] text-ellipsis overflow-hidden whitespace-nowrap">{{item.filePath}}</span>
            <span class="text-sm text-[var(--text-tertiary)]">{{item.createdAt | relative}}</span>
          </div>
          <div
            class="flex items-center justify-center cursor-pointer rounded-md w-8 h-8 opacity-0 group-hover:opacity-100"
            type="button"
            [cdkMenuTriggerFor]="menu"
            [cdkMenuTriggerData]="{file: item}"
          >
            <i class="ri-more-line"></i>
          </div>
        </div>
      }
    </div>
  </div>
</div>

<ng-template #menu let-file="file">
  <div cdkMenu class="cdk-menu__medium">
    <div cdkMenuItem (click)="preview(file)">
        {{'PAC.Chat.Preview' | translate: {Default: 'Preview'} }}
    </div>
    <div cdkMenuItem (click)="download(file)">
        {{'PAC.Chat.Download' | translate: {Default: 'Download'} }}
    </div>
  </div>
</ng-template>