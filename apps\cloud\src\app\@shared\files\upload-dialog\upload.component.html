<div class="ngm-theme-dark dark w-[300px] shrink-0 flex flex-col justify-start overflow-auto bg-bluegray-700 text-white p-4 group">
  <div class="w-full flex justify-start items-center mb-4" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
    <mat-icon displayDensity="cosy" class="-ml-2 opacity-0 group-hover:opacity-80">drag_indicator</mat-icon>
    <span class="text-lg pointer-events-none">
      {{ 'PAC.Project.Files' | translate: { Default: 'Files' } }}
    </span>
  </div>
  
  <ngm-search class="my-2" [formControl]="searchControl"></ngm-search>
  
  <mat-nav-list class="ngm-nav-list flex-1 overflow-auto" />

  <div class="flex justify-end items-center">
    <div ngmButtonGroup>
      <button mat-stroked-button ngmAppearance="dashed" displayDensity="cosy" color="accent" (click)="upload()">
        <div class="flex justify-start items-center">
          <mat-icon fontSet="material-icons-round">upload</mat-icon>
          {{ 'Story.Common.Upload' | translate: {Default: "Upload"} }}
        </div>
      </button>
    </div>
  </div>
</div>

<div class="flex-1 min-w-[300px] flex flex-col justify-center items-center overflow-hidden">
  <pac-upload class="w-full flex-1"
    [files]="fileList"
    (filesChange)="onFileListChange($event)"
    (removeFileChange)="removeFiles($event)"
  />
</div>
