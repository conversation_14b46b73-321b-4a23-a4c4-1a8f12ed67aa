<div class="shrink-0 flex items-center justify-between px-4 pt-3 pb-2 text-text-primary system-xl-semibold">
  <div class="h-8">{{'PAC.Xpert.Preview' | translate: {Default: 'Preview'} }}</div>
  <div class="flex items-center gap-1">

    <ng-content></ng-content>

    <button type="button" class="action-btn action-btn-sm primary w-7 h-7"
      [matTooltip]="'PAC.Xpert.Restart' | translate: {Default: 'Restart'}"
      (click)="onRestart()">
      <i class="ri-restart-line font-normal text-base"></i>
    </button>

    <div class="mx-2 w-[1px] h-3.5 bg-divider-deep"></div>

    <div class="action-btn action-btn-sm danger" (click)="onClose()">
      <i class="ri-close-large-line w-4 h-4 flex justify-center items-center text-sm"></i>
    </div>
  </div>
</div>

<div class="grow flex flex-col rounded-b-2xl overflow-y-auto">
  <div class="flex-1 relative overflow-x-hidden px-3 pt-4">
    @if (parameters()?.length) {
      <xpert-parameters-card class="w-full mb-4" [parameters]="parameters()"
        [(ngModel)]="parameterValue"
      />
    }

    @for (message of messages(); track message.id) {
      @switch (message.role) {
        @case ('human') {
          <div class="flex justify-end mb-2 last:mb-0 pl-10">
            <div class="group relative mr-4">
              <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute -right-2 top-0 w-2 h-3 text-[#D1E9FF]/50" data-icon="QuestionTriangle" aria-hidden="true">
                <g id="Rectangle 2">
                  <path d="M6.96353 1.5547C7.40657 0.890144 6.93018 0 6.13148 0H0V12L6.96353 1.5547Z" fill="currentColor"></path>
                  <path d="M6.96353 1.5547C7.40657 0.890144 6.93018 0 6.13148 0H0V12L6.96353 1.5547Z" fill="currentColor" fill-opacity="0.5"></path>
                </g>
              </svg>
              <div class="px-4 py-3 bg-[#D1E9FF]/50 rounded-b-2xl rounded-tl-2xl text-sm text-gray-900">
                <div class="markdown-body">
                  <p>{{message.content}}</p>
                </div>
              </div>
              <div class="mt-1 h-[18px]"></div>
            </div>
            <div class="shrink-0 w-10 h-10">
              <div class="w-full h-full rounded-full border-[0.5px] border-black/5">
                <svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-full h-full" data-icon="User" aria-hidden="true">
                  <g clip-path="url(#clip0_5968_39205)">
                    <rect width="512" height="512" rx="256" fill="#B2DDFF"></rect>
                    <circle opacity="0.68" cx="256" cy="196" r="84" fill="white"></circle>
                    <ellipse opacity="0.68" cx="256" cy="583.5" rx="266" ry="274.5" fill="white"></ellipse>
                  </g>
                  <defs>
                    <clipPath id="clip0_5968_39205">
                      <rect width="512" height="512" rx="256" fill="white"></rect>
                    </clipPath>
                  </defs>
                </svg>
              </div>
            </div>
          </div>
        }

        @case ('ai') {
          <div class="flex mb-2 last:mb-0">
            <emoji-avatar [avatar]="avatar()" class="rounded-lg overflow-hidden shadow-sm" />
            <div class="chat-answer-container group grow w-0 ml-4">
              <div class="group relative pr-2 flex">
                <svg width="8" height="12" viewBox="0 0 8 12" fill="none" xmlns="http://www.w3.org/2000/svg" class="absolute -left-2 top-0 w-2 h-3 text-gray-100" data-icon="AnswerTriangle" aria-hidden="true">
                  <path id="Rectangle 1" d="M1.03647 1.5547C0.59343 0.890144 1.06982 0 1.86852 0H8V12L1.03647 1.5547Z" fill="currentColor"></path>
                </svg>
                <div class="grow-0 relative inline-block px-2 py-3 max-w-full bg-gray-100 rounded-b-2xl rounded-tr-2xl text-base text-gray-900">
                  <xpert-preview-ai-message [message]="message" [conversation]="conversation()" class="w-full grow" />
                </div>

                <div class="absolute flex justify-end items-center gap-1 right-0 -top-4">

                  <div class="group-hover:flex justify-center items-center p-1 rounded-lg bg-white border-[0.5px] border-gray-100 shadow-sm shrink-0"
                    [ngClass]="{
                      hidden: !(synthesizeLoading() || isPlaying()),
                      flex: synthesizeLoading() || isPlaying()
                    }">
                    <div class="shrink-0 w-6 h-6 flex items-center justify-center rounded-[6px] font-medium
                      bg-components-card-bg text-gray-500 hover:bg-gray-50 cursor-pointer hover:text-gray-700"
                      [matTooltip]="'PAC.KEY_WORDS.Copy' | translate: {Default: 'Copy'} "
                      matTooltipPosition="above"
                      (click)="copy(message)"
                    >
                      @if (copiedMessages()[message.id]) {
                        <i class="ri-clipboard-fill"></i>
                      } @else {
                        <i class="ri-clipboard-line"></i>
                      }
                    </div>

                    @if (textToSpeech_enabled()) {
                      <div class="shrink-0 w-6 h-6 flex items-center justify-center rounded-[6px] font-medium
                        bg-components-card-bg text-gray-500 hover:bg-gray-50 cursor-pointer hover:text-gray-700"
                        [matTooltip]="'PAC.Xpert.ReadAloud' | translate: {Default: 'Read aloud'} "
                        matTooltipPosition="above"
                        (click)="readAloud(message)"
                      >
                        @if (synthesizeLoading() || isPlaying()) {
                          <i class="ri-stop-circle-line text-primary-500"></i>
                        } @else {
                          <i class="ri-speak-ai-line"></i>
                        }
                      </div>
                    }
                  </div>

                @if (message.executionId) {
                  <div class="hidden group-hover:flex items-center w-max h-[28px] p-1 rounded-lg bg-white border-[0.5px] border-gray-100 shadow-sm shrink-0">
                    <div class="shrink-0 p-1 flex items-center justify-center rounded-[6px] font-medium text-gray-500 hover:bg-gray-50 cursor-pointer hover:text-gray-700"
                      (click)="openExecution(message)"
                    >
                      <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" class="mr-1 w-4 h-4">
                        <g id="Icon">
                          <path id="Icon_2" d="M9.33366 7.3335H5.33366M6.66699 10.0002H5.33366M10.667 4.66683H5.33366M13.3337 4.5335V11.4668C13.3337 12.5869 13.3337 13.147 13.1157 13.5748C12.9239 13.9511 12.618 14.2571 12.2416 14.4488C11.8138 14.6668 11.2538 14.6668 10.1337 14.6668H5.86699C4.74689 14.6668 4.18683 14.6668 3.75901 14.4488C3.38269 14.2571 3.07673 13.9511 2.88498 13.5748C2.66699 13.147 2.66699 12.5869 2.66699 11.4668V4.5335C2.66699 3.41339 2.66699 2.85334 2.88498 2.42552C3.07673 2.04919 3.38269 1.74323 3.75901 1.55148C4.18683 1.3335 4.74689 1.3335 5.86699 1.3335H10.1337C11.2538 1.3335 11.8138 1.3335 12.2416 1.55148C12.618 1.74323 12.9239 2.04919 13.1157 2.42552C13.3337 2.85334 13.3337 3.41339 13.3337 4.5335Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"></path>
                        </g>
                      </svg>
                      <div class="text-sm leading-4">{{'PAC.Xpert.ExecutionLog' | translate: {Default: 'Execution Log'} }}</div>
                    </div>
                    <div class="mx-1 w-[1px] h-[14px] bg-gray-200"></div>
                  </div>
                }

                @if (getFeedback(message.id); as feedback) {
                  @if (feedback.rating === eFeedbackRatingEnum.LIKE) {
                    <div class="flex items-center justify-center m-1 w-6 h-6 rounded-md cursor-pointer bg-primary-100 text-primary-500 hover:bg-primary-100 hover:text-primary-600"
                      [matTooltip]="'PAC.KEY_WORDS.CancelLike' | translate: {Default: 'Cancel like'}"
                      matTooltipPosition="above"
                      (click)="cancelFeedback(message, feedback.id)"
                    >
                      <i class="ri-thumb-up-line"></i>
                    </div>
                  } @else if (feedback.rating === eFeedbackRatingEnum.DISLIKE) {
                    <div class="flex items-center justify-center m-1 w-6 h-6 rounded-md cursor-pointer bg-red-100 text-red-500 hover:bg-red-200 hover:text-red-600"
                      [matTooltip]="'PAC.KEY_WORDS.CancelDislike' | translate: {Default: 'Cancel dislike'}"
                      matTooltipPosition="above"
                      (click)="cancelFeedback(message, feedback.id)"
                    >
                      <i class="ri-thumb-down-line"></i>
                    </div>
                  }
                } @else {
                  <div class="hidden group-hover:flex shrink-0 items-center p-1 bg-white border-[0.5px] border-gray-100 shadow-md text-gray-500 rounded-lg">
                    <div class="flex items-center justify-center mr-0.5 w-6 h-6 rounded-md hover:bg-black/5 hover:text-gray-800 cursor-pointer"
                      [matTooltip]="'PAC.KEY_WORDS.Like' | translate: {Default: 'Like'}"
                      matTooltipPosition="above"
                      (click)="feedback(message, eFeedbackRatingEnum.LIKE)"
                    >
                      <i class="ri-thumb-up-line"></i>
                    </div>
                    <div class="flex items-center justify-center w-6 h-6 rounded-md hover:bg-black/5 hover:text-gray-800 cursor-pointer"
                      [matTooltip]="'PAC.KEY_WORDS.Dislike' | translate: {Default: 'Dislike'}"
                      matTooltipPosition="above"
                      (click)="feedback(message, eFeedbackRatingEnum.DISLIKE)"
                    >
                      <i class="ri-thumb-down-line"></i>
                    </div>
                  </div>
                }
                </div>
              </div>
              <div class="flex items-center mt-1 h-[18px] text-xs text-gray-400 opacity-0 group-hover:opacity-100"></div>
            </div>
          </div>
        }
      }        
    } @empty {
      <div class="absolute flex flex-col justify-start items-center p-4 w-full max-w-full top-1/2 -translate-y-1/2">
        <div class="flex justify-center mb-2">
          <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 text-gray-300">
            <g id="chat-bot"><g id="Vector"><path d="M13.0909 11.2727C14.0951 11.2727 14.9091 10.4587 14.9091 9.45455C14.9091 8.45039 14.0951 7.63636 13.0909 7.63636C12.0868 7.63636 11.2727 8.45039 11.2727 9.45455C11.2727 10.4587 12.0868 11.2727 13.0909 11.2727Z" fill="currentColor"></path>
              <path d="M20.3636 22.1818H7.63636C5.62727 22.1818 4 23.8091 4 25.8182V40.3636C4 42.3727 5.62727 44 7.63636 44H33.0909C35.1 44 36.7273 42.3727 36.7273 40.3636V25.8182M13.0909 15.9998V11.2727M13.0909 11.2727C14.0951 11.2727 14.9091 10.4587 14.9091 9.45455C14.9091 8.45039 14.0951 7.63636 13.0909 7.63636C12.0868 7.63636 11.2727 8.45039 11.2727 9.45455C11.2727 10.4587 12.0868 11.2727 13.0909 11.2727ZM27.6364 5.81818C27.6364 4.81455 28.4509 4 29.4545 4H42.1818C43.1855 4 44 4.81455 44 5.81818V14.9091C44 15.9127 43.1855 16.7273 42.1818 16.7273H33.0909L27.6364 20.3636V5.81818Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
            </g>
            <path id="Vector_2" d="M15.7275 30.364C15.7275 31.3179 14.9542 32.0913 14.0002 32.0913C13.0463 32.0913 12.2729 31.3179 12.2729 30.364C12.2729 29.41 13.0463 28.6367 14.0002 28.6367C14.9542 28.6367 15.7275 29.41 15.7275 30.364ZM28.4548 30.364C28.4548 31.3179 27.6814 32.0913 26.7275 32.0913C25.7735 32.0913 25.0002 31.3179 25.0002 30.364C25.0002 29.41 25.7735 28.6367 26.7275 28.6367C27.6814 28.6367 28.4548 29.41 28.4548 30.364Z" fill="currentColor" stroke="currentColor" stroke-width="2"></path>
            </g>
          </svg>
        </div>
        
        <div class="w-[256px] text-center text-[13px] text-gray-400">
          {{ 'PAC.Xpert.StartDebuggingDigitalExpert' | translate: {Default: 'Enter your content in the box below to start debugging Digital Expert'} }}
        </div>

        @if (starters()?.length) {
          <div class="w-full flex items-center mt-4 leading-[18px] text-sm text-gray-500 uppercase">
            <div class="grow w-0 h-px bg-divider-deep"></div>
            <div class="mx-3">{{ 'PAC.Xpert.YouMightAsk' | translate: {Default: 'You might want to ask'} }}</div>
            <div class="grow w-0 h-px bg-divider-deep"></div>
          </div>

          <div class="w-full max-w-full flex flex-wrap justify-start items-start gap-2 text-sm overflow-hidden p-4 text-text-secondary">
            @for (starter of starters(); track starter) {
              <div class="max-w-full px-2 py-1 border-light rounded-lg cursor-pointer truncate shadow-sm hover:shadow-md"
                (click)="chat({input: starter})"
              >{{starter}}</div>
            }
          </div>
        }
      </div>
    }

    @if (suggestion_enabled()) {
      @if (suggesting()) {
        <div class="flex justify-center items-center cursor-pointer">
          <i class="ri-loader-2-line flex justify-center items-center w-3.5 h-3.5 animate-spin"></i>
          <div class="text-sm text-text-tertiary ml-2">
            {{ 'PAC.Xpert.Suggesting' | translate: {Default: 'Suggesting'} }}...
          </div>
        </div>
      } @else if (suggestionQuestions()?.length) {
        <div class="mb-2 py-2">
          <div class="mb-2.5 flex items-center justify-between gap-2">
            <div class="w-full my-2 bg-gradient-to-r from-divider-regular to-divider-deep h-px grow rotate-180"></div>
            <div class="shrink-0 text-text-tertiary">
            {{ 'PAC.Xpert.TryAsking' | translate: {Default: 'Try asking'} }}
          </div>
            <div class="w-full my-2 bg-gradient-to-r from-divider-regular to-divider-deep h-px grow"></div>
          </div>
          <div class="flex flex-wrap justify-center">
            @for (question of suggestionQuestions(); track question) {
              <button type="button" class="btn disabled:btn-disabled btn-secondary btn-small mb-1 mr-1 last:mr-0"
                (click)="onSelectSuggestionQuestion(question)">{{question}}</button>
            }
          </div>
        </div>
      }
    }
    
    @if (conversationStatus() === eExecutionStatusEnum.INTERRUPTED && operation()) {
      <xpert-tool-call-confirm class="w-full bg-components-card-bg"
        [readonly]="readonly()"
        [operation]="operation()"
        (toolCallsChange)="onToolCalls($event)"
        (confirm)="onConfirm()"
        (reject)="onReject()"
      />
    }

    @if (conversationStatus() === eExecutionStatusEnum.ERROR) {
      <div class="w-full flex items-start gap-2 p-4 rounded-3xl text-orange-500 bg-orange-50 border border-solid border-orange-200">
        <div >
          <i class="ri-information-2-line text-xl"></i>
        </div>
        <div class="flex-1 py-1 break-all">{{error()}}</div>
    
        <button type="button" class="btn btn-small text-text-primary" (click)="onRetry()">
          {{ 'PAC.KEY_WORDS.Retry' | translate: {Default: 'Retry'} }}
        </button>
      </div>
    }

    <div class="pt-6 w-full max-w-full mx-auto"></div>
  </div>
  

  @if(!readonly()) {
    <div class="sticky bottom-0 px-4 rounded-bl-2xl" style="background: linear-gradient(0deg, rgb(239, 247, 255) 40%, rgba(255, 255, 255, 0) 100%);">
      <div class="pb-4 w-full max-w-full mx-auto">
        <div class="relative">
          <div class="p-[5.5px] max-h-[150px] bg-white border-[1.5px] border-gray-200 rounded-xl mb-2">
            <textarea class="outline-none w-full pl-2 pr-16"
              [(ngModel)]="input"
              (keydown)="onKeydown($event)"
            ></textarea>
            <div class="absolute bottom-[7px] flex items-center h-8 right-2 z-10">
              <div class="flex items-center px-1 h-5 rounded-md bg-gray-100 text-xs font-medium text-gray-500">{{inputLength()}}</div>
              <div class="mx-2 w-[1px] h-4 bg-black opacity-5"></div>

              @if (attachment_enabled()) {
                <button type="button" class="action-btn action-btn-l w-8 h-8 flex justify-center items-center rounded-lg" >
                  <i class="ri-attachment-line text-lg"></i>
                </button>
              }

              @if (speechToText_enabled()) {
                @if (speeching()) {
                  
                } @else {
                  <button type="button" class="action-btn action-btn-l w-8 h-8 flex justify-center items-center rounded-lg"
                    (click)="startRecording()">
                    <i class="ri-mic-ai-fill text-lg"></i>
                  </button>
                }
              }

              @if (loading()) {
                <button class="w-8 h-8 flex justify-center items-center rounded-full border-light hover:shadow-md"
                  (click)="onStop()">
                  <i class="ri-stop-fill"></i>
                </button>
              } @else {
                <button class="group action-btn action-btn-md primary"
                  [disabled]="loading() || !input()"
                  (click)="chat({input: input()})">
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" class="w-5 h-5">
                    <g id="send-03">
                      <path id="Solid" d="M18.4385 10.5535C18.6111 10.2043 18.6111 9.79465 18.4385 9.44548C18.2865 9.13803 18.0197 8.97682 17.8815 8.89905C17.7327 8.81532 17.542 8.72955 17.3519 8.64403L3.36539 2.35014C3.17087 2.26257 2.97694 2.17526 2.81335 2.11859C2.66315 2.06656 2.36076 1.97151 2.02596 2.06467C1.64761 2.16994 1.34073 2.4469 1.19734 2.81251C1.07045 3.13604 1.13411 3.44656 1.17051 3.60129C1.21017 3.76983 1.27721 3.9717 1.34445 4.17418L2.69818 8.25278C2.80718 8.58118 2.86168 8.74537 2.96302 8.86678C3.05252 8.97399 3.16752 9.05699 3.29746 9.10816C3.44462 9.1661 3.61762 9.1661 3.96363 9.1661H10.0001C10.4603 9.1661 10.8334 9.53919 10.8334 9.99943C10.8334 10.4597 10.4603 10.8328 10.0001 10.8328H3.97939C3.63425 10.8328 3.46168 10.8328 3.3148 10.8905C3.18508 10.9414 3.07022 11.0241 2.98072 11.1309C2.87937 11.2519 2.82459 11.4155 2.71502 11.7428L1.3504 15.8191C1.28243 16.0221 1.21472 16.2242 1.17455 16.3929C1.13773 16.5476 1.07301 16.8587 1.19956 17.1831C1.34245 17.5493 1.64936 17.827 2.02806 17.9327C2.36342 18.0263 2.6665 17.9309 2.81674 17.8789C2.98066 17.8221 3.17507 17.7346 3.37023 17.6467L17.3518 11.355C17.542 11.2695 17.7327 11.1837 17.8815 11.0999C18.0197 11.0222 18.2865 10.861 18.4385 10.5535Z" fill="currentColor"></path>
                    </g>
                  </svg>
                </button>
              }
            </div>

            <div class="absolute left-0 bottom-0 w-full h-12 border border-solid border-gray-200 rounded-lg bg-white shadow-md z-10 p-2 overflow-hidden"
              [ngClass]="{
                'hidden': !speeching(),
                'block': speeching()
              }"
            >
              <canvas #waveCanvas class="absolute left-0 top-0 w-full h-12 z-0"></canvas>
              <div class="absolute right-0 left-0 flex items-center px-4 z-10">
                <div class="grow">
                  @if (isRecording())  {
                    <span>{{'PAC.Xpert.Speaking' | translate: {Default: 'Speaking'} }}...</span>
                  } @else if (isConverting()) {
                    <span>{{'PAC.Xpert.Converting' | translate: {Default: 'Converting'} }}...</span>
                  }
                </div>
                <button type="button" class="action-btn action-btn-l w-8 h-8 flex justify-center items-center rounded-lg"
                  (click)="stopRecording()">
                  <i class="ri-stop-circle-line text-primary-500"></i>
                </button>
                @if (isRecording()) {
                  <div>
                    {{recordTimes() * 1000 | date:'mm:ss'}}
                  </div>
                }
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  }
</div>