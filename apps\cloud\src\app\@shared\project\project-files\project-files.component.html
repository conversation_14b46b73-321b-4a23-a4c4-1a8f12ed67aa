<div class="ngm-theme-dark dark w-[300px] shrink-0 flex flex-col justify-start overflow-auto bg-bluegray-700 text-white p-4 group">
    <div class="w-full flex justify-start items-center mb-4" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
      <mat-icon displayDensity="cosy" class="-ml-2 opacity-0 group-hover:opacity-80">drag_indicator</mat-icon>
      <span class="text-lg pointer-events-none">
        {{ 'PAC.Project.Files' | translate: { Default: 'Files' } }}
      </span>
    </div>
  
    <ngm-search class="my-2" [formControl]="searchControl"></ngm-search>
  
    <mat-nav-list class="ngm-nav-list flex-1 overflow-auto">
      @for (file of filteredFiles(); track file.id) {
        <mat-list-item class="group/item"
            [class.active]="activedFile()?.id === file.id" 
            (click)="activeLink(file)"
        >
            <div class="flex items-center">
                <span class="flex-1 text-ellipsis overflow-hidden whitespace-nowrap"> {{ file.originalName }} </span>

                <button mat-icon-button ngmAppearance="danger" displayDensity="cosy" class="opacity-0 group-hover/item:opacity-100"
                    (click)="deleteFile($event, file)">
                    <mat-icon fontSet="material-icons-round">delete</mat-icon>
                </button>
            </div>
        </mat-list-item>
      }
    </mat-nav-list>

    <input #fileUpload type="file" class="file-input h-0 invisible" (change)="onFileSelected($event)" (click)="fileUpload.value=null;" accept="*">

    <div class="flex justify-end items-center">
      @if (activedFile()) {
        <div ngmButtonGroup>
            <button mat-stroked-button ngmAppearance="dashed" displayDensity="cosy" color="accent" (click)="fileUpload.click()">
                <div class="flex justify-start items-center">
                  <mat-icon fontSet="material-icons-round">upload</mat-icon>
                  {{ 'Story.Common.Upload' | translate: {Default: "Upload"} }}
                </div>
            </button>
        </div>
      }
    </div>
</div>

<div class="flex-1 min-w-[300px] flex flex-col justify-center items-center overflow-hidden">
  @if (activedFile()) {
    <div class="w-full flex-1 flex flex-col justify-start items-stretch relative">
        <div class="absolute top-0 left-0 w-full p-2 text-ellipsis bg-clip-text text-transparent bg-gradient-to-r from-pink-500 to-violet-500">
            <span >{{activedFile().originalName}}: {{activedFile().url}}</span>
        </div>
      @if (isImage(activedFile())) {
        <img class="flex-1" [src]="activedFile().url"/>
      }
    </div>
  } @else {
    <button mat-stroked-button ngmAppearance="dashed" color="accent"
        (click)="fileUpload.click()"
    >
        <div class="flex justify-start items-center">
          <mat-icon fontSet="material-icons-round">upload</mat-icon>
          {{ 'Story.Common.Upload' | translate: {Default: "Upload"} }}
        </div>
    </button>
  }
</div>
