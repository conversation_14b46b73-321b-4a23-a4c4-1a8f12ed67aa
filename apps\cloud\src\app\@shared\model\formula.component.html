<div class="flex flex-1 max-w-full" cdkDropListGroup>
  <div class="ngm-theme-dark dark w-[400px] shrink-0 flex flex-col justify-start overflow-auto bg-bluegray-700 text-white p-4 group" >
    <div class="w-full flex justify-start items-center my-2" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
      <mat-icon displayDensity="cosy" class="-ml-2 opacity-0 group-hover:opacity-80">drag_indicator</mat-icon>
      <span class="text-lg pointer-events-none">
        {{ 'PAC.KEY_WORDS.CalculatedFormula' | translate: {Default: 'Calculated Formula'} }}
      </span>
      <span *ngIf="measure?.caption">({{measure.caption}})</span>
    </div>

    <div class="flex-1 flex flex-col justify-start items-stretch overflow-hidden">
      <ngm-copilot-chat class="flex-1 h-full w-full"></ngm-copilot-chat>
    </div>

    <div class="flex">
      <div ngmButtonGroup>
        <button mat-raised-button color="accent" [matDialogClose]="formula">
          {{ ('COMPONENTS.COMMON.Apply' | translate: {Default: 'Apply'}) }}
        </button>

        <button mat-button mat-dialog-close cdkFocusInitial >
          {{ ('COMPONENTS.COMMON.CANCEL' | translate: {Default: 'Cancel'}) }}
        </button>
      </div>
    </div>
  </div>
  
  <div class="min-w-[500px] flex-1 flex overflow-hidden">
    @switch (syntax) {
      @case (Syntax.MDX) {
        <ngm-calculated-measure class="flex w-full h-full"
          [syntax]="Syntax.MDX"
          [dataSettings]="dataSettings"
          [entityType]="entityType"
          [(ngModel)]="formula"
        >
        </ngm-calculated-measure>
      }
      @case(Syntax.SQL) {
        <ngm-formula-editor class="flex w-full h-full"
          [editorOptions]="{
            theme: themeName()
          }"
          [dataSettings]="dataSettings"
          [(ngModel)]="formula"
        >
        </ngm-formula-editor>
      }
    }
  </div>
</div>