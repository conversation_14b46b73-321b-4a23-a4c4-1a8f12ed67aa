<header class="px-4 pt-2" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
  <h4 class="text-xl font-semibold cursor-move">
    {{ 'PAC.Xpert.AuthorizationMethod' | translate: { Default: 'Authorization method' } }}
  </h4>
</header>
  
<div class="flex p-4">
  <div class="space-y-4">
    <div>
      <div class="py-2 leading-5 text-sm font-medium">
        {{ 'PAC.Xpert.AuthorizationType' | translate: { Default: 'Authorization type' } }}
      </div>

      <ngm-radio-select [(ngModel)]="authType" [selectOptions]="AuthSelectOptions" />
    </div>

    @if (authType() === eApiProviderAuthType.BASIC) {
      <div>
        <div class="py-2 leading-5 text-sm font-medium">{{'PAC.KEY_WORDS.Username' | translate: {Default: 'Username'} }}</div>
        <xpert-variable-input class="w-full p-2 text-sm font-normal rounded-lg grow bg-gray-50" 
          type="text" autocomplete="username" 
          [placeholder]="'PAC.KEY_WORDS.EnterUsername' | translate: {Default: 'Enter username'}"
          [variables]="variables()" 
          [(ngModel)]="username"
        />

      </div>
      <div>
        <div class="py-2 leading-5 text-sm font-medium">{{'PAC.KEY_WORDS.Password' | translate: {Default: 'Password'} }}</div>
        <xpert-variable-input class="w-full p-2 text-sm font-normal rounded-lg grow bg-gray-50" 
          type="password" autocomplete="current-passwor" 
          [placeholder]="'PAC.KEY_WORDS.EnterPassword' | translate: {Default: 'Enter password'}"
          [variables]="variables()" 
          [(ngModel)]="password"
        />
      </div>

    } @else if (authType() === eApiProviderAuthType.API_KEY) {
      <div class="py-2 leading-5 text-sm font-medium">
        {{'PAC.Xpert.AuthType' | translate: {Default: 'Auth Type'} }}
      </div>

      <ngm-radio-select [(ngModel)]="api_key_type" [selectOptions]="PrefixSelectOptions" />
      
      @if (api_key_type() === 'custom') {
        <div>
          <div class="flex items-center h-8 text-[13px] font-medium">
            {{ 'PAC.KEY_WORDS.Key' | translate: {Default: 'Key'} }}
            <div class="ml-0.5 w-4 h-4" [matTooltip]="'PAC.Xpert.HttpHeaderKey' | translate: {Default: 'Http Header Key, you can leave it with `Authorization` if you have no idea what it is or set it to custom value'} ">
              <svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" class="text-text-quaternary hover:text-text-tertiary w-full h-full">
                <path d="M12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22ZM12 20C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4C7.58172 4 4 7.58172 4 12C4 16.4183 7.58172 20 12 20ZM11 15H13V17H11V15ZM13 13.3551V14H11V12.5C11 11.9477 11.4477 11.5 12 11.5C12.8284 11.5 13.5 10.8284 13.5 10C13.5 9.17157 12.8284 8.5 12 8.5C11.2723 8.5 10.6656 9.01823 10.5288 9.70577L8.56731 9.31346C8.88637 7.70919 10.302 6.5 12 6.5C13.933 6.5 15.5 8.067 15.5 10C15.5 11.5855 14.4457 12.9248 13 13.3551Z"></path>
              </svg>
            </div>
          </div>
          <input class="ngm-input w-full h-10 px-3 text-sm font-normal rounded-lg grow" placeholder="HTTP header name for API Key"
            [(ngModel)]="api_key_header"
            autocomplete="off"
          >
        </div>
      }
      <div>
        <div class="py-2 leading-5 text-sm font-medium">
          {{ 'PAC.KEY_WORDS.Value' | translate: {Default: 'Value'} }}
        </div>

        <xpert-variable-input class="w-full p-2 text-sm font-normal rounded-lg grow bg-gray-50" 
          type="password" autocomplete="current-password" [placeholder]="'PAC.Xpert.EnterAPIKey' | translate: {Default: 'Enter API Key'}"
          [variables]="variables()" 
          [(ngModel)]="api_key_value"
        />
      </div>
    }
  </div>
</div>

<div class="w-full flex justify-end p-2">
  <div class="flex items-center gap-2">
    <button type="button" class="btn disabled:btn-disabled btn-large"
      (click)="cancel()"
    >
      {{ 'PAC.ACTIONS.CANCEL' | translate: { Default: 'Cancel' } }}
    </button>

    <button type="button" class="btn disabled:btn-disabled btn-primary btn-large"
      (click)="save()"
    >
      <i class="ri-save-line mr-1"></i>
      {{ 'PAC.ACTIONS.Save' | translate: { Default: 'Save' } }}
    </button>
  </div>
</div>