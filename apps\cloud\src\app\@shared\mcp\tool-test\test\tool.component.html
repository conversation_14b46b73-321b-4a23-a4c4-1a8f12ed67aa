<div>
  <div class="w-full flex justify-between items-center p-2">
    <div class="py-2 leading-5 text-sm font-medium text-gray-900">
      {{ 'PAC.Xpert.Parameters' | translate: { Default: 'Parameters' } }} &amp; {{ 'PAC.Xpert.Value' | translate: { Default: 'Value' } }}</div>
    <button type="button" class="btn btn-medium h-8"
      [disabled]="!parameters()"
      (click)="saveAsDefault()">
      {{ 'PAC.Xpert.SaveAsDefault' | translate: { Default: 'Save as default' } }}</button>
  </div>
  
  <div class="rounded-lg border border-gray-200 max-h-80 p-2 overflow-y-auto">
    @if (parameterList()) {
      <table class="w-full leading-[18px] text-sm text-gray-700 font-normal">
        <thead class="text-gray-500 uppercase">
          <tr class="border-b border-gray-200">
            <th class="p-2 pl-3 font-medium text-left">{{ 'PAC.Xpert.Parameters' | translate: { Default: 'Parameters' } }}</th>
            <th class="p-2 pl-3 font-medium text-left">{{ 'PAC.Xpert.Value' | translate: { Default: 'Value' } }}</th>
          </tr>
        </thead>
        <tbody>
          @for (parameter of parameterList(); track parameter.name) {
            <tr class="border-b last:border-0 border-gray-200">
              <td class="py-2 pl-3 pr-2.5">{{parameter.name}}
                @if (parameter.required) {
                  <span class="inline-block text-sm font-medium text-[#EC4A0A]">*</span>
                }
              </td>
              <td class="">
                <input class="ngm-input-inline ngm-input-sm w-full" [type]="parameter.type || 'text' "
                  [ngModel]="parameters()?.[parameter.name]"
                  (ngModelChange)="onParameter(parameter.name, $event)">
              </td>
            </tr>
          }
        </tbody>
      </table>
    } @else if(jsonSchema()) {
      <json-schema-form [schema]="jsonSchema()" [(ngModel)]="parameters" />
    }
  </div>
</div>

<div class="flex gap-2 items-center mt-4 ">
  <button type="button" class="flex-1 btn disabled:btn-disabled btn-primary h-10 
    active:scale-x-[0.99] active:scale-y-95"
    [disabled]="loading() || invalid()"
    (click)="testTool()"
  ><div class="w-full text-center">{{ 'PAC.Xpert.Test' | translate: { Default: 'Test' } }}</div></button>

  @if (loading()) {
    <button type="button" class="btn disabled:btn-disabled btn-danger h-10 pressable"
      (click)="stopTestTool()"
    >
      <div class="w-full text-center">
        <i class="ri-stop-line"></i>
        {{ 'PAC.Xpert.Stop' | translate: { Default: 'Stop' } }}
      </div>
    </button>
  }
</div>

<div class="relative mt-6">
  <div class="flex items-center space-x-3">
    <div class="leading-[18px] text-sm font-semibold text-gray-500">
      {{ 'PAC.Xpert.TestResults' | translate: { Default: 'Test Results' } }}
    </div>
    <div class="grow w-0 h-px bg-[rgb(243, 244, 246)]"></div>
  </div>
  <div class="mt-2 px-3 py-2 h-[200px] overflow-auto rounded-lg leading-4 text-sm font-normal whitespace-pre-wrap
    bg-gray-100 text-gray-700">
    @if (testResult()) {
      {{testResult()}}
    } @else {
      <span class="text-gray-400">{{ 'PAC.Xpert.TestResultsHere' | translate: { Default: 'Test result will show here' } }}</span>
    }
  </div>

  @if (loading()) {
    <ngm-spin class="absolute top-0 left-0 w-full h-full" />
  }

</div>
