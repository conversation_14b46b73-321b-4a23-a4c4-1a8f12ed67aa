<h3 class="text-2xl font-semi-bold text-text-primary">
  {{ 'PAC.Xpert.SelectReferenceKnowledge' | translate: {Default: 'Select reference Knowledge'} }}
</h3>
<ul class="mt-7 max-h-[286px] space-y-1 overflow-y-auto"
  cdkListbox
  cdkListboxMultiple
  [(ngModel)]="selected"
>
  @for (item of knowledgebases(); track item.id) {
    <li #option="cdkOption" class="flex h-10 cursor-pointer items-center justify-between rounded-lg px-2 border-[1.5px] bg-state-accent-hover shadow-xs hover:shadow-xs"
      [class.selected]="option.isSelected()"
      [cdkOption]="item.id"
    >
      <div class="mr-1 flex items-center overflow-hidden">
        <div class="mr-2 flex items-center justify-center rounded-lg w-7 h-7 bg-slate-50 border-[0.5px] border-solid border-slate-200 text-primary-500">
          <i class="ri-book-shelf-fill text-lg"></i>
        </div>
        <div class="max-w-[200px] truncate text-[13px] font-medium text-text-secondary">
          {{item.name}}
        </div>
      </div>
      <!-- <div class="relative inline-flex h-5 items-center rounded-[5px] border border-divider-deep px-[5px] leading-3 text-text-tertiary system-2xs-medium-uppercase shrink-0">
        HQ · VECTOR
      </div> -->
    </li>
  }
</ul>
<div class="mt-8 flex items-center justify-between">
  <div class="text-sm font-medium text-text-secondary">{{selected()?.length ?? 0}} Knowledgebase selected</div>
  <div class="flex space-x-2">
    <button type="button" class="btn disabled:btn-disabled btn-secondary btn-large"
      (click)="cancel()">
      {{ 'PAC.ACTIONS.Cancel' | translate: {Default: 'Cancel'} }}
    </button>
    <button type="button" class="btn disabled:btn-disabled btn-primary btn-large"
      (click)="apply()">
      {{ 'PAC.ACTIONS.Add' | translate: {Default: 'Add'} }}
    </button>
  </div>
</div>
