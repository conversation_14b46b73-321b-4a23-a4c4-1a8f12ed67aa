<div class="flex flex-col gap-4">
  <div class="cursor-move text-2xl font-medium text-text-primary" cdkDrag cdkDragRootElement=".cdk-overlay-pane" cdkDragHandle>
    {{ 'PAC.SHARED.User.CreateAccounts' | translate: {Default: 'Create Accounts'} }}
  </div>

  <ngm-stepper class="overflow-hidden space-y-8 mb-8" [(ngModel)]="step"
    [steps]="steps" />
</div>

<div class="m-6 border-l border-solid border-divider-regular"></div>

<div class="relative w-[600px] min-h-[400px] flex flex-col gap-2 mt-8 mb-2">
  @switch (step()) {
    @case (1) {
      <pac-upload class="flex-1"
        [files]="fileList"
        (filesChange)="onFileListChange($event)"
        (removeFileChange)="removeFiles($event)"
      />

      <div class="w-full flex justify-end">
        <button
          type="button"
          class="btn disabled:btn-disabled btn-secondary btn-large mr-2"
          (click)="downloadTempl()"
        >
          <i class="ri-table-view font-normal mr-1"></i>
          {{ 'PAC.SHARED.User.DownloadTemplate' | translate: {Default: "Download Template"} }}</button>
      </div>
    }

    @case (2) {
      <ngm-table class="flex-1" [data]="users()" [columns]="[
        {
          name: 'username',
          caption: ('PAC.SHARED.USER_BASIC.Username' | translate: {Default: 'Username'})
        },
        {
          name: 'email',
          caption: ('PAC.SHARED.USER_BASIC.Email' | translate: {Default: 'Email'})
        },
        {
          name: 'hash',
          caption:  ('PAC.SHARED.USER_BASIC.Passwrod' | translate: {Default: 'Passwrod'})
        },
        {
          name: 'firstName',
          caption:  ('PAC.SHARED.USER_BASIC.firstName' | translate: {Default: 'First Name'})
        },
        {
          name: 'lastName',
          caption:  ('PAC.SHARED.USER_BASIC.lastName' | translate: {Default: 'Last Name'})
        },
        {
          name: 'roleName',
          caption:  ('PAC.SHARED.USER_BASIC.Role' | translate: {Default: 'Role'})
        },
        {
          name: 'thirdPartyId',
          caption:  ('PAC.SHARED.USER_BASIC.ThirdPartyId' | translate: {Default: 'Third Party Id'})
        }
      ]"/>

      <div class="w-full flex justify-end">
        <button type="button" class="btn disabled:btn-disabled btn-primary btn-large"
          [disabled]="loading() || !users().length"
          (click)="save()"
        >
          <i class="ri-save-line mr-1"></i>{{ 'PAC.ACTIONS.Save' | translate: {Default: "Save"} }}</button>
      </div>
    }
  }

  @if (loading()) {
    <ngm-spin class="absolute top-0 left-0 w-full h-full" />
  }
</div>

<button type="button" class="btn rounded-xl justify-center absolute top-4 right-4 w-8 h-8"
  (click)="close()">
  <i class="ri-close-line"></i>
</button>